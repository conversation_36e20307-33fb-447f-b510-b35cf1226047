#!/usr/bin/env pwsh

# Script to fix Html.BeginForm parameter issues for .NET 8 upgrade
# Adds the missing parameters required by ASP.NET Core

Write-Host "Fixing Html.BeginForm parameter issues..." -ForegroundColor Green

# Get all CSHTML files
$files = Get-ChildItem -Path . -Recurse -Filter "*.cshtml" | Where-Object { $_.FullName -notlike "*\bin\*" -and $_.FullName -notlike "*\obj\*" }

$fixedCount = 0
$totalFiles = 0

foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
    $originalContent = $content
    $fileChanged = $false
    
    # Pattern 1: Html.BeginForm(action, controller, FormMethod.Post, htmlAttributes)
    # Should become: Html.BeginForm(action, controller, null, FormMethod.Post, true, htmlAttributes)
    $pattern1 = 'Html\.BeginForm\(([^,]+),\s*([^,]+),\s*(FormMethod\.Post),\s*([^)]+)\)'
    $replacement1 = 'Html.BeginForm($1, $2, null, $3, true, $4)'
    
    if ($content -match $pattern1) {
        $content = $content -replace $pattern1, $replacement1
        $fileChanged = $true
        Write-Host "  Fixed Pattern 1 in: $($file.FullName)" -ForegroundColor Yellow
    }
    
    # Pattern 2: Html.BeginForm(action, controller, routeValues, FormMethod.Post, htmlAttributes)
    # Should become: Html.BeginForm(action, controller, routeValues, FormMethod.Post, true, htmlAttributes)
    $pattern2 = 'Html\.BeginForm\(([^,]+),\s*([^,]+),\s*(new\s*\{\s*[^}]*\}),\s*(FormMethod\.Post),\s*([^)]+)\)'
    $replacement2 = 'Html.BeginForm($1, $2, $3, $4, true, $5)'
    
    if ($content -match $pattern2) {
        $content = $content -replace $pattern2, $replacement2
        $fileChanged = $true
        Write-Host "  Fixed Pattern 2 in: $($file.FullName)" -ForegroundColor Yellow
    }
    
    # Pattern 3: Html.BeginForm() - simple form
    # Should become: Html.BeginForm(null, null, null, FormMethod.Post, true, null)
    $pattern3 = 'Html\.BeginForm\(\s*\)'
    $replacement3 = 'Html.BeginForm(null, null, null, FormMethod.Post, true, null)'
    
    if ($content -match $pattern3) {
        $content = $content -replace $pattern3, $replacement3
        $fileChanged = $true
        Write-Host "  Fixed Pattern 3 in: $($file.FullName)" -ForegroundColor Yellow
    }
    
    # Pattern 4: Html.BeginForm(action, controller) - no FormMethod specified
    # Should become: Html.BeginForm(action, controller, null, FormMethod.Post, true, null)
    $pattern4 = 'Html\.BeginForm\(([^,]+),\s*([^,)]+)\)\s*(?!\s*,)'
    $replacement4 = 'Html.BeginForm($1, $2, null, FormMethod.Post, true, null)'
    
    if ($content -match $pattern4) {
        $content = $content -replace $pattern4, $replacement4
        $fileChanged = $true
        Write-Host "  Fixed Pattern 4 in: $($file.FullName)" -ForegroundColor Yellow
    }
    
    # Pattern 5: Html.BeginForm(action, controller, routeValues) - no FormMethod specified
    # Should become: Html.BeginForm(action, controller, routeValues, FormMethod.Post, true, null)
    $pattern5 = 'Html\.BeginForm\(([^,]+),\s*([^,]+),\s*(new\s*\{\s*[^}]*\})\)\s*(?!\s*,)'
    $replacement5 = 'Html.BeginForm($1, $2, $3, FormMethod.Post, true, null)'
    
    if ($content -match $pattern5) {
        $content = $content -replace $pattern5, $replacement5
        $fileChanged = $true
        Write-Host "  Fixed Pattern 5 in: $($file.FullName)" -ForegroundColor Yellow
    }
    
    if ($fileChanged) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        $fixedCount++
    }
    
    $totalFiles++
}

Write-Host "`nSummary:" -ForegroundColor Green
Write-Host "  Total files scanned: $totalFiles" -ForegroundColor Cyan
Write-Host "  Files with fixes applied: $fixedCount" -ForegroundColor Cyan

# Build to check for remaining errors
Write-Host "`nBuilding to check for remaining BeginForm errors..." -ForegroundColor Green
$buildOutput = dotnet build --no-restore 2>&1 | Out-String
$remainingErrors = ($buildOutput | Select-String "BeginForm.*CS7036").Count

Write-Host "Remaining BeginForm CS7036 errors: $remainingErrors" -ForegroundColor $(if ($remainingErrors -eq 0) { "Green" } else { "Yellow" })

if ($remainingErrors -gt 0) {
    Write-Host "`nRemaining errors (first 10):" -ForegroundColor Yellow
    $buildOutput | Select-String "BeginForm.*CS7036" | Select-Object -First 10 | ForEach-Object {
        Write-Host "  $_" -ForegroundColor Red
    }
}
