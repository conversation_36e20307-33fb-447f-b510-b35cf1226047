# PowerShell script to fix RZ1027 errors (missing closing parentheses)
# Get all RZ1027 errors from build output

$buildOutput = dotnet build Cerebrum30.sln --no-restore 2>&1 | Select-String "RZ1027"

foreach ($error in $buildOutput) {
    if ($error -match "(.+\.cshtml)\((\d+),(\d+)\): error RZ1027") {
        $file = $matches[1]
        $line = [int]$matches[2]
        $column = [int]$matches[3]
        
        Write-Host "Processing: $file at line $line"
        
        # Read the file content
        $content = Get-Content $file -Raw
        $lines = Get-Content $file
        
        if ($line -le $lines.Count) {
            $problemLine = $lines[$line - 1]
            Write-Host "Problem line: $problemLine"
            
            # Common patterns for missing closing parentheses
            # Pattern 1: if statement with missing closing paren
            if ($problemLine -match '^(\s*@?if\s*\([^)]*)\s*$') {
                $fixed = $problemLine + ")"
                $lines[$line - 1] = $fixed
                $lines | Set-Content $file
                Write-Host "Fixed: Added closing parenthesis to if statement"
            }
            # Pattern 2: method call with missing closing paren
            elseif ($problemLine -match '^(\s*@?[^(]+\([^)]*)\s*$') {
                $fixed = $problemLine + ")"
                $lines[$line - 1] = $fixed
                $lines | Set-Content $file
                Write-Host "Fixed: Added closing parenthesis to method call"
            }
            # Pattern 3: foreach statement with missing closing paren
            elseif ($problemLine -match '^(\s*foreach\s*\([^)]*)\s*$') {
                $fixed = $problemLine + ")"
                $lines[$line - 1] = $fixed
                $lines | Set-Content $file
                Write-Host "Fixed: Added closing parenthesis to foreach statement"
            }
        }
    }
}

Write-Host "Completed fixing RZ1027 errors"