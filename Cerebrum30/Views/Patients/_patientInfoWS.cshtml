﻿@model Cerebrum.ViewModels.Patient.VMPatientInfo
@{
    int ageCaluculated = 0;
    if (!String.IsNullOrEmpty(@Model.DOB))
    {
        ageCaluculated = Convert.ToInt32(DateTime.Now.Year) - Convert.ToInt32(Convert.ToDateTime(@Model.DOB).Year);
    }

    bool showAppointmentPriority = CerebrumUser.IsAppointmentPriorityEnabled && Convert.ToBoolean(ViewBag.IsPriorityExists) && CerebrumUser.IsPracticeAppointmentPriorityEnabled;
}

<!-- http://localhost:65062/Measurements/Measurement?AppointmentID=5316&Testid=12&AppointmentTestID=7946 -->
<link href="~/Content/patient-info.css" rel="stylesheet" />
<script src="~/Scripts/patient-info.js"></script>
<script>
    $(document).ready(function () {
        var patientObj = {};
        patientObj.id= @Model.PatientId;
        patientObj.firstname = '@Model.FirstName';
        patientObj.lastname = '@Model.LastName.ToUpper()';
        var patientAgeStr = '@Model.PatientAge';

        var elementWidths = $('.Z').map(function() {
            return $(this).width();
        }).get();

        var maxWidth = Math.max.apply(null, elementWidths);
        $('.Z').width(maxWidth);

        $(document).on('click','#dialog7 a',function(){
            $('#dialog7').dialog('close');
        });

        $('.ctrl-eChart7').on('click', function(){
            loadEChartDialogeFromPatientInfo(patientObj);
        });

        if(patientAgeStr != null)
        {
            var liZcore = $('#li-zscore');
            var patientAge = parseInt(patientAgeStr);

            if(patientAge >=0 && patientAge <= 16 && liZcore.length > 0)
            {
                var appTestLogId = $("#frm-ws #AppointmentTestLogID").val();
                var button = '<button data-app-test-log-id="'+appTestLogId+'" id="btn-view-zcore" class="btn btn-primary btn-xs btn-spacing">View Z-score</button>';
                $('#li-zscore #div-zcore-container').html(button);
            }
        }
    });
</script>
@{await Html.RenderPartialAsync("_patientInfoAppointmentPriorities", Model); }
<div class="row __patient-top-pnl __786873459">
    <div class="col-xs-12 col-sm-12 col-md-4 col-lg-3">
        <div class="float-Left-Label"><span><b>Patient:</b></span><span style="font-size: 20px;" id="cbr-patient-fullname"> @Model.LastName.ToUpper(), @Model.FirstName @Model.MiddleName @Html.Raw(string.IsNullOrWhiteSpace(Model.PreferredName) ? "" : "[" + Model.PreferredName + "]")</span></div>
        <div class="ctrl-eChart7 float-Left-Label"><span class="glyphicon glyphicon-duplicate"></span>&nbsp; e-Chart</div>
    </div>
    <div class="col-xs-12 col-sm-12 col-md-8 col-lg-9">

        <div class="float-Left-Label Z"><span><b>Age:</b> @Model.AgeAccurate &nbsp;&nbsp;(DOB: @Model.DOB - mm/dd/yyyy)</span></div>
        <div class="float-Left-Label Z"><span><b>Gender:</b> @Model.Gender </span></div>
        <div class="float-Left-Label Z"><span><b>HIN:</b> @Model.OHIP @Model.OHIPVersionCode</span></div>
        <div class="float-Left-Label Z"><span><b>Family Dr.:</b> @Model.FamilyDoctor</span></div>
        @if (showAppointmentPriority)
        {
            <div class="float-Left-Label Z">
                <span><b>Priority:</b> <span id="patient-info-appointment-priority-name">@Model.PriorityName</span></span>
                <div data-appointment-id="@Model.AppointmentID" class="glyphicon glyphicon-pencil btn-patient-info-appointment-priority"></div>
            </div>
        }
        <div class="float-Left-Label Z"><span><b>Referral Dr.:</b> @Model.ReferralDoctor</span></div>

        @if ((!string.IsNullOrWhiteSpace(Model.TestReferralDoctor)) && (!Model.ReferralDoctor.ToLower().Equals(Model.TestReferralDoctor.ToLower())))
        {
            <div class="float-Left-Label Z"><span><b>Test Referral Dr.:</b> @Model.TestReferralDoctor</span></div>
        }
        <div class="float-Left-Label Z"><span><b>MRP.:</b> @Model.MRPDoctor</span></div>
        <div class="float-Left-Label Z"><span><b>Test Name:</b> @Model.TestName</span></div>
        <div class="float-Left-Label Z">
            @if(Model.AppointmentDate.HasValue)
            {
                <span><strong> Visit Date:</strong> @Model.AppointmentDate.Value.ToLongDateString() - @Model.AppointmentDate.Value.ToShortTimeString() </span>
            }
            &nbsp;
        </div>
    </div>
</div>

@*<div class="row __patient-top-pnl __786873459">
        <div class="col-xs-11 col-sm-11 col-md-11 col-lg-11">
            <div>
                <div class="float-Left-Label adjust-width" style="font-size: 24px; margin-top:-4px"><span><strong>Patient:</strong></span><span id="cbr-patient-fullname"> @Model.LastName.ToUpper(), @Model.FirstName @@</span></div>
                <div class="float-Left-Label" adjust-width><span><strong>Birth Date</strong> (mm/dd/yyyy)<strong>:</strong> @Model.DOB </span></div>
                <div class="float-Left-Label adjust-width"><span><strong>Age:</strong> @Model.Age </span></div>
                <div class="float-Left-Label adjust-width"><span><strong>Gender:</strong> @Model.Gender </span></div>
                <div class="float-Left-Label adjust-width"><span><strong>Ohip:</strong> @Model.OHIP</span></div>
                <div class="float-Left-Label adjust-width"><span><strong>Family Dr.:</strong> @Model.FamilyDoctor</span></div>
                <div class="float-Left-Label adjust-width"><span><strong>Referral Dr.:</strong> @Model.ReferralDoctor</span></div>
                <div class="float-Left-Label adjust-width"><span><strong>MRP:</strong> @Model.MRPDoctor</span></div>
                <div class="float-Left-Label adjust-width"><span><strong>Visit Date:</strong> @Model.AppointmentDate.Value.ToLongDateString() - @Model.AppointmentDate.Value.ToShortTimeString()</span></div>
                <div class="float-Left-Label adjust-width"><span><strong>Test Name:</strong> @Model.TestName</span></div>

                </div>
            </div>

            <div class="col-xs-1 col-sm-1 col-md-1 col-lg-1">
                <div class="margin-left-15 float_r" style="white-space: nowrap">
                    <div id="sidebar-right-open"><div class="c-pointer" onclick="openSidebar()"><span class="glyphicon glyphicon-list f25"></span> <span>e-Chart</span></div></div>
                </div>
            </div>
    </div>*@

<link href="~/Areas/Schedule/Content/shared-styles.css" rel="stylesheet" />
@*<div id="mmm">
        <a href="javascript:void(0)" class="closebtn" onclick="closeSidebar()">&times;</a>
        @{Html.RenderAction("GetPatientMenu", "Patients", new { area = "", Id = Model.PatientId, AppointmentId = Model.AppointmentID }); }
    </div>*@







