﻿@model Cerebrum.ViewModels.Common.VMExternalDoctor

<style>
    #frm-edit-ext-doctor .not-active {
        pointer-events: none;
        cursor: default;
        text-decoration: none;
        color: black;
    }

    /*#frm-edit-ext-doctor #ext-doc-address-holder{
        max-height: 200px;
        overflow-y:auto;
    }*/
</style>



<div>

    @Html.ModalHeader("Read Only Doctor :" + Model.LastName)

    <div class="modal-body content-height500">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>

        <div class="form-horizontal">

            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.OHIP, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.OHIP, new { htmlAttributes = new { @class = "form-control " + @Model.billNumber_ro } })
                    @Html.ValidationMessageFor(model => model.OHIP, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.PhysicianType, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10 ">
                    @foreach (var ptype in Enum.GetNames(typeof(AwareMD.Cerebrum.Shared.Enums.PhysicianType)))
                    {
                        @Html.RadioButtonFor(model => model.PhysicianType, ptype, new { htmlAttributes = new { @class = "form-control" } }) @ptype
                        <span style="padding-right: 12px;"></span>
                    }
                    @Html.ValidationMessageFor(model => model.PhysicianType, "", new { @class = "text-danger" })
                </div>
            </div>
            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.FirstName, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10 ">
                    @Html.EditorFor(model => model.FirstName, new { htmlAttributes = new { @class = "form-control " + @Model.firstNmae_ro } })
                    @Html.ValidationMessageFor(model => model.FirstName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.LastName, htmlAttributes: new { @class = "control-label col-md-2 required-label" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.LastName, new { htmlAttributes = new { @class = "form-control " + @Model.lastNmae_ro } })
                    @Html.ValidationMessageFor(model => model.LastName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.MiddleName, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10  focus">
                    @Html.EditorFor(model => model.MiddleName, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.MiddleName, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Email, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Email, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Addresses, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <div id="ext-doc-address-holder" data-url="@Url.Action("GetExternalDoctorAddresses","externaldoctors",new { area="" })" data-external-doctor-id="@Model.Id">
                        @{await Html.RenderPartialAsync("_externalDoctorAddressListReadOnly", Model.Addresses); }
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Description, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Description, new { @class = "form-control", @rows = 3 })
                    @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Comment, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.TextAreaFor(model => model.Comment, new { @class = "form-control", @rows = 3 })
                    @Html.ValidationMessageFor(model => model.Comment, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.HRMId, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-10">

                    <div style="margin-right:5px;" class="pull-left">
                        @Html.EditorFor(model => model.HRMId, new { htmlAttributes = new { @class = "form-control" } })
                        @Html.ValidationMessageFor(model => model.HRMId, "", new { @class = "text-danger" })
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.IsActive, new { htmlAttributes = new { @disabled = "disabled" } }) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsActive)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.IsLocked, new { htmlAttributes = new { @disabled = "disabled" } }) <span class="checkbox-text">@Html.DisplayNameFor(model => model.IsLocked)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                <label class="control-label col-md-2">Contact By</label>
                <div class="col-md-10">
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.ContactByHRM, new { htmlAttributes = new { @disabled = "disabled" } })<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByHRM)</span>
                            </label>
                        </div>
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.ContactByEmail,new { htmlAttributes = new { @disabled = "disabled" } })<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByEmail)</span>
                            </label>
                        </div>
                    </div>
                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.ContactByFax,new { htmlAttributes = new { @disabled = "disabled" } })<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByFax)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.ContactByPhone, new { htmlAttributes = new { @disabled = "disabled" } })<span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByPhone)</span>
                            </label>
                        </div>
                    </div>

                    <div style="margin-right:10px;" class="pull-left">
                        <div class="checkbox">
                            <label>
                                @Html.EditorFor(model => model.ContactByMail, new { htmlAttributes = new { @disabled = "disabled" } }) <span class="checkbox-text">@Html.DisplayNameFor(model => model.ContactByMail)</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.Password, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.Password, new { htmlAttributes = new { @class = "form-control" } })
                    @Html.ValidationMessageFor(model => model.Password, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group form-group-sm">
                @Html.LabelFor(model => model.CPSO, htmlAttributes: new { @class = "control-label col-md-2" })
                <div class="col-md-2">
                    @Html.EditorFor(model => model.CPSO, new { htmlAttributes = new { @class = "form-control " + @Model.cpso_ro } })
                    @Html.ValidationMessageFor(model => model.CPSO, "", new { @class = "text-danger" })
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-default btn-xs ext-cpso-btn">Check CPSO in HRM</button>
                </div>
                <div class="col-md-6">
                    <a target="_blank" href="https://doctors.cpso.on.ca/?v=doctors">CPSO Doctor Search</a>
                </div>
            </div>
        </div>

    </div><!--Modal body ends-->

    <div class="modal-footer ">
        <button type="button" class="btn btn-default btn-sm btn-primary" style="margin-right: 48px;" onclick="return printDoctorAddressLabel(@Model.Id)">Print Address Label</button>
        <button type="button" class="btn btn-default btn-sm " data-dismiss="modal">Close</button>
    </div>

    <script type="text/javascript">
        setTimeout(function () { $('.focus :input').focus() }, 1000);

        function printDoctorAddressLabel(externalDoctorId) {
            var addressIds = $("#frm-edit-ext-doctor input[name='addressCheckboxPrinting']:checkbox:checked").map(function () {
                return this.value
            }).get();

            window.open("Externaldoctors/PrintDoctorAddressLabel?externalDoctorId=" + externalDoctorId + "&addressIds=" + addressIds);
            return false;
        }
    </script>
</div>

