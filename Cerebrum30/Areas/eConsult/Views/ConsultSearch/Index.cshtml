﻿@using Cerebrum.BLL.Utility;
@using AwareMD.Cerebrum.Shared.Enums;
@{
    ViewBag.Title = "Search eConsults";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
    var IsOtnSpecialist = (bool)ViewBag.IsOtnSpecialist;
}
<script src="~/Scripts/moment.js"></script>
<script src="~/Scripts/daterangepicker.js"></script>
<script src="~/Areas/eConsult/Scripts/search-consults.js"></script>
<link href="~/Content/daterangepicker.css" rel="stylesheet" />
<link href="~/Areas/eConsult/Content/index.css" rel="stylesheet" />

<h4>
    <i>Search Active Consults</i>

    <span class="pull-right">
        <span class="inline-block">Page size:</span>

        <span style="display:inline-block; padding-right:15px;">
            <select id="ddlPageSize" class="form-control input-sm">
                <option value="5">5</option>
                <option value="10" selected>10</option>
                <option value="25">25</option>
                <option value="50">50</option>
            </select>
        </span>
    </span>
</h4>
<div class="text-center">@ViewBag.OtnDoctorName</div>
<h5>show more data <input type="checkbox" id="chkShowMoreData"></h5>

<input type="hidden" id="hdDateSubmittedStart" value="@DateTime.Now.AddMonths(-1).ToString("yyyy-MM-dd")"/>
<input type="hidden" id="hdDateSubmittedEnd" value="@DateTime.Now.ToString("yyyy-MM-dd")" />
<table class="table-serach-active-consult">
    <tr>
        <td>
            <div class="col-sm-12">
                <br />
                <table class="table table-condensed table-bordered table-responsive">
                    <thead>
                        <tr>
                            <th>
                                <table>
                                    <tr>
                                        <td>Case Id</td>
                                    </tr>
                                </table>
                            </th>
                            <th style="min-width:120px;">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="subject">Subject</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-subject-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-subject-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th style="min-width:100px;">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" title="Sort By Last Name" class="btn-consult-sort-column" data-sort-column="patient">Patient</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-patient-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-patient-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            @if (IsOtnSpecialist)
                            {
                                <th style="min-width:100px;">
                                    <table>
                                        <tr>
                                            <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="requester">Requester</a></td>
                                            <td>
                                                <table>
                                                    <tr><td id="td-requester-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                    <tr><td id="td-requester-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </th>
                            }
                            <th style="min-width:100px;">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="recipient">Recipient</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-recipient-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-recipient-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th>
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="state">State</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-state-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-state-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-nowrap">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="submittedDate">Submitted Date</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-submittedDate-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-submittedDate-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-nowrap show-more-data">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="lastUpdatedDate">Last Updated</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-lastUpdatedDate-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-lastUpdatedDate-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-nowrap show-more-data">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="billingCoverage">Billing</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-billingCoverage-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-billingCoverage-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                             
                            <th class="text-center">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="savedFlag">Saved<br /> Flag</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-savedFlag-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-savedFlag-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-center text-nowrap">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="needsToBeSeen">Needs to<br /> be seen</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-needsToBeSeen-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-needsToBeSeen-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            <th class="text-center text-nowrap">
                                <table>
                                    <tr>
                                        <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="patientAssociated">Patient<br /> associated</a></td>
                                        <td>
                                            <table>
                                                <tr><td id="td-patientAssociated-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                <tr><td id="td-patientAssociated-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                            @if (IsOtnSpecialist)
                            {
                                <th class="text-center">
                                    <table>
                                        <tr>
                                            <td><a href="javascript:void(null)" class="btn-consult-sort-column" data-sort-column="consultFlag">Consult<br /> Flag</a></td>
                                            <td>
                                                <table>
                                                    <tr><td id="td-consultFlag-up" class="td-sort-order" style="display:none">&#9650;</td></tr>
                                                    <tr><td id="td-consultFlag-down" class="td-sort-order" style="display:none">&#9660;</td></tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </th>
                            }
                            <th>

                            </th>
                        </tr>
                        <tr>
                            <td style="width:60px;">
                                <input type="text" id="txtCaseId" placeholder="Case Id" style="min-width:55px;font-size:9px;" class="form-control input-sm" maxlength="12" />
                            </td>
                            <td>
                                @*<input type="text" id="txtSubject" placeholder="Subject" class="form-control input-sm" />*@
                            </td>
                            <td>
                                <input type="text" id="txtPatientLastName" style="font-size:9px;" placeholder="Patient Last Name" class="form-control input-sm" />
                            </td>
                            @if (IsOtnSpecialist)
                            {
                                <td>
                                    <input type="text" id="txtRequesterName" style="font-size:9px;" placeholder="Requester Name" class="form-control input-sm" />
                                </td>
                            }
                            <td>
                                <input type="text" id="txtRecipientName" style="font-size:9px;" placeholder="Recipient Name" class="form-control input-sm" />
                            </td>
                            <td>
                                <select class="form-control input-sm" id="ddlStatus" style="font-size:9px;width:120px;height:25px;">
                                    <option value="">All</option>
                                    <option value="Assigned">Assigned</option>
                                    <option value="Cancelled">Cancelled</option>
                                    <option value="Clarification Requested">Clarification Requested</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Consult Provided">Consult Provided</option>
                                    <option value="More Info Provided">More Info Provided</option>
                                    <option value="More Info Requested">More Info Requested</option>
                                    <option value="Requested">Requested</option>
                                    <option value="Returned">Returned</option>
                                    <option value="Submitted">Submitted</option>
                                    <option value="Unassigned">Unassigned</option>
                                </select>
                            </td>
                            <td class="text-nowrap"  style="min-width:110px;">
                                <input type="text" class="form-control input-sm datepicker" style="font-size:9px;width:110px;" id="submittedDatedaterange" name="submittedDatedaterange" value="@DateTime.Now.AddMonths(-1).ToString("MM/dd/yyyy") - @DateTime.Now.ToString("MM/dd/yyyy")" />
                            </td>
                            <td class="text-nowrap show-more-data">
                                <input id="LastUpdatedDate" name="LastUpdatedDate" style="font-size:9px;" type="text" class="form-control input-sm datepicker" />
                            </td>
                            <td class="text-nowrap show-more-data"></td>
                            <td class="text-nowrap" style="min-width:65px;">
                                <select class="form-control input-sm" id="ddlSavedFlag" style="font-size:9px;height:25px;">
                                    <option value="0">All</option>
                                    <option value="1">Yes</option>
                                    <option value="2">No</option>
                                </select>
                            </td>
                            <td class="text-center text-nowrap" style="min-width:65px;">
                                <select class="form-control input-sm" id="ddlNeedsToBeSeen" style="font-size:9px;height:25px;">
                                    <option value="0">All</option>
                                    <option value="1">Yes</option>
                                    <option value="2">No</option>
                                </select>
                            </td>
                            <td class="text-center text-nowrap" style="min-width:65px;">
                                <select class="form-control input-sm" id="ddlPatientAssociated" style="font-size:9px;height:25px;">
                                    <option value="0">All</option>
                                    <option value="1">Yes</option>
                                    <option value="2">No</option>
                                </select>
                            </td>
                            @if (IsOtnSpecialist)
                            {
                                <td style="min-width:100px;">
                                    <select id="ddlSearchConsultFlag" class="form-control input-sm" style="font-size:9px;height:25px;">
                                        <option value="">All</option>
                                        @foreach (var item in Enum.GetValues(typeof(ConsultFlag)))
                                        {
                                            <option value="@item.ToString()">@UtilityHelper.GetDescriptionFromEnumValue((ConsultFlag)item)</option>
                                        }
                                    </select>
                                </td>
                            }
                            <td>
                                <input type="button" class="btn btn-primary btn-sm" style="font-size:9px;padding:3px 5px;" value="Search" id="btnSearchActiveConsult" />
                            </td>

                        </tr>
                    </thead>
                    <tbody id="tbody-consult-search-result"></tbody>
                </table>
            </div>
        </td>
    </tr>
    <tr>
        <td colspan="14" class="text-center">
            <span style="display:none; padding-top:1px;" id="span-loading-list-main">
                <img src="~/Content/Images/loading.gif" style="width:20px;" />
            </span>
        </td>
    </tr>
</table>
