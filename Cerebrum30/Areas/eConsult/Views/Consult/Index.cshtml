﻿@model Cerebrum.ViewModels.Econsult.VMCareTeam
@{
    ViewBag.Title = "eConsults";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
    var cerebrumUser = (Cerebrum30.Security.CerebrumUser)ViewBag.CerebrumUser;
    var listOfTeam = Model.listOfTeam.Where(t => t.Role != "DELEGATE").ToList();

    var doctorName = listOfTeam[0].Name;
    // don't show dropdown of delegators if there is only one (first is delegate)
    if (cerebrumUser.IsOtnDelegate)
    {
        if (listOfTeam.Count != 1)
        {
            if (cerebrumUser.OtnDelegatorPractitionerId == "" || cerebrumUser.OtnDelegatorUserId == "")
            {
                doctorName = string.Empty;
            }
            else
            {
                doctorName = listOfTeam.Where(t => t.PractitionerId == cerebrumUser.OtnDelegatorPractitionerId).FirstOrDefault().Name;
            }
        }
    }
    var showMenu = (bool)ViewBag.ShowMenu;
    var isFromEchart = (bool)ViewBag.IsFromEchart;
    var eChartPatientId = (int)ViewBag.EchartPatientId;
    var eChartPatientDraftId = (int)ViewBag.EchartPatientDraftId;
    var eChartPatientConsultId = (string)ViewBag.EchartPatientConsultId;
    var isShowUnlinkButton = (bool)ViewBag.IsShowUnlinkButton;
    //var IsAllowToRefresh = (bool)ViewBag.IsAllowToRefresh;

}

<link href="~/Areas/eConsult/Content/index.css" rel="stylesheet" />
<script src="~/Areas/eConsult/Scripts/index.js"></script>

<input type="hidden" id="IsOtnSpecialist" value="@cerebrumUser.IsOtnSpecialist.ToString().ToLower()" />
<input type="hidden" id="IsShowMenu" value="@showMenu.ToString().ToLower()" />
<input type="hidden" id="IsFromEchart" value="@isFromEchart.ToString().ToLower()" />
<input type="hidden" id="EchartPatientId" value="@eChartPatientId" />
<input type="hidden" id="EchartPatientDraftId" value="@eChartPatientDraftId" />
<input type="hidden" id="EchartPatientConsultId" value="@eChartPatientConsultId" />
<input type="hidden" id="SelectedTab" value="@ViewBag.SelectedTab" />
@*<input type="hidden" id="TokenExpirationDateMilliseconds" value="@ViewBag.TokenExpirationDateMilliseconds" />*@
<input type="hidden" id="IsShowUnlinkButton" value="@isShowUnlinkButton.ToString().ToLower()" />
@*<input type="hidden" id="IdpSessionTimeout" value="@ViewBag.IdpSessionTimeout" />*@
@*<input type="hidden" id="IsAllowToRefresh" value="@IsAllowToRefresh.ToString().ToLower()" />*@
@Html.AntiForgeryToken()

<div class="container" style="width:auto;">
    <div class="row">
        <div class="col-md-2"><h3><button class="btn btn-primary" id="btnRequestConsult">Request Consult</button></h3></div>
        <div class="col-md-8"><h3 class="text-center" id="DoctorName">@doctorName</h3></div>
        <div class="col-md-2 pull-right">
            @if (cerebrumUser.IsOtnDelegate && listOfTeam.Count > 1)
            {
            <h3>
                <select class="form-control" id="ddlDelegator" name="ddlDelegator">
                    <option value="0">Select Delegator</option>
                    @for (var i = 0; i < listOfTeam.Count; i++)
                        {
                            if (cerebrumUser.OtnDelegatorPractitionerId == listOfTeam[i].PractitionerId)
                            {
                                <option value="@listOfTeam[i].PractitionerId" selected
                                        data-otn-user-id="@listOfTeam[i].OtnUserId"
                                        data-practitioner-id="@listOfTeam[i].PractitionerId"
                                        data-role="@listOfTeam[i].Role">
                                    @listOfTeam[i].Name
                                </option>
                            }
                            else
                            {
                                <option value="@listOfTeam[i].PractitionerId"
                                        data-otn-user-id="@listOfTeam[i].OtnUserId"
                                        data-practitioner-id="@listOfTeam[i].PractitionerId"
                                        data-role="@listOfTeam[i].Role">
                                    @listOfTeam[i].Name
                                </option>
                            }
                        }
                </select>
            </h3>
            }
        </div>
    </div>

    <div class="row">
        <div id="divConsult">
            <ul class="nav nav-tabs">
                <li style="color:#000;font-size:large;padding:10px; height:40px;">Requests</li>
                <li class="active"><a data-toggle="tab" href="#needsAttention" id="NeedsAttention">Needs Attention <span id="needsAttentionCount"></span></a></li>
                <li><a data-toggle="tab" href="#waitingForResponse" id="WaitingForResponse">Waiting For Response <span id="waitingForResponseCount"></span></a></li>
                <li><a data-toggle="tab" href="#completed" id="Completed">Completed <span id="completedCount"></span></a></li>
                <li><a data-toggle="tab" href="#cancelled" id="Cancelled">Cancelled <span id="cancelledCount"></span></a></li>
                <li><a data-toggle="tab" href="#drafts" id="Drafts">Drafts <span id="draftsCount"></span></a></li>
                <li><a data-toggle="tab" href="#reportsAsRequester" id="ReportsAsRequester">Reports as Requester</a></li>

                <li style="color:#000;font-size:large;padding:10px; height:40px;" class="specialist-area">Consults</li>
                <li class="specialist-area"><a data-toggle="tab" href="#needsAttentionResponder" id="NeedsAttentionResponder" class="specialist-area">Needs Attention <span id="needsAttentionResponderCount"></span></a></li>
                <li class="specialist-area"><a data-toggle="tab" href="#waitingForMoreInfoResponder" id="WaitingForMoreInfoResponder" class="specialist-area">Waiting for More Info <span id="waitingForMoreInfoResponderCount"></span></a></li>

                <li class="specialist-area"><a data-toggle="tab" href="#consultProvidedResponder" id="ConsultProvidedResponder" class="specialist-area">Consult Provided <span id="consultProvidedResponderCount"></span></a></li>
                <li class="specialist-area"><a data-toggle="tab" href="#consultCompletedResponder" id="ConsultCompletedResponder" class="specialist-area">Completed <span id="consultCompletedResponderCount"></span></a></li>
                <li class="specialist-area"><a data-toggle="tab" href="#consultReturnedResponder" id="ConsultReturnedResponder" class="specialist-area">Consult Returned <span id="consultReturnedResponderCount"></span></a></li>
                <li class="specialist-area"><a data-toggle="tab" href="#reportsAsSpecialist" id="ReportsAsSpecialist" class="specialist-area">Reports as Specialist</a></li>
                <li style="padding-left:15px;" id="liUnlinkOneIdAccount"><button class="btn btn-primary btn-sm" id="btnUnlinkOneIdAccount">Unlink OneId Account</button></li>
                <li>@Html.ActionLink("Log out from OneId", "LogoutFromOneId", "Consult", new { @class = "btn btn-default btn-sm" })</li>

            </ul>
            <div class="tab-content">
                <div id="needsAttention" class="tab-pane fade in active overflow-hidden"></div>
                <div id="waitingForResponse" class="tab-pane fade overflow-hidden"></div>
                <div id="completed" class="tab-pane fade overflow-hidden"></div>
                <div id="cancelled" class="tab-pane fade overflow-hidden"></div>
                <div id="drafts" class="tab-pane fade overflow-hidden"></div>
                <div id="reportsAsRequester" class="tab-pane fade overflow-hidden"></div>

                <div id="needsAttentionResponder" class="tab-pane fade specialist-area overflow-hidden"></div>
                <div id="waitingForMoreInfoResponder" class="tab-pane fade specialist-area overflow-hidden"></div>

                <div id="consultProvidedResponder" class="tab-pane fade specialist-area overflow-hidden"></div>
                <div id="consultCompletedResponder" class="tab-pane fade specialist-area overflow-hidden"></div>
                <div id="consultReturnedResponder" class="tab-pane fade specialist-area overflow-hidden"></div>
                <div id="reportsAsSpecialist" class="tab-pane fade specialist-area overflow-hidden"></div>
            </div>
        </div>
    </div>
</div>
<br /><br />
