﻿@model  Cerebrum.ViewModels.Econsult.Models.OtnConsult
@using AwareMD.Cerebrum.Shared.Enums;
@using Cerebrum.BLL.Utility;
@{
    var showConsultFlag = false;
    var disabled = "disabled";
    if (ViewBag.ShowConsultFlag != null)
    {
        showConsultFlag = (bool)ViewBag.ShowConsultFlag;
        if (showConsultFlag && Model.ConsultStatus != "Completed")
        {
            disabled = "";
        }
    }

    var btnStyleAssociatePatient = "";
    var btnStyleDisassociatePatient = "";
    if (!Model.EmrInitialRequest)
    {
        if (Model.PatientAssociatedWithEmr)
        {
            btnStyleAssociatePatient = "style=display:none;";
            btnStyleDisassociatePatient = "style=display:block;";
        }
        else
        {
            btnStyleAssociatePatient = "style=display:block;";
            btnStyleDisassociatePatient = "style=display:none;";
        }
    }
    var billingCoverage = "Not available";
    if (Model.CoverageId > 0)
    {
        billingCoverage = UtilityHelper.GetDescriptionFromEnumValue((BillingCoverageType)Model.CoverageId);
    }

    var showButtonSaveConsult = ViewBag.ShowButtonSaveConsult == null ? false : (bool)ViewBag.ShowButtonSaveConsult;

}
<script>
    $(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });
</script>
<table style="width:100%;" class="table table-condensed borderless">
    <tr>
        <td>
            Requester
        </td>
        <td>
            @Model.RequesterName
        </td>
    </tr>
    <tr>
        <td>
            Recipient
        </td>
        <td>
            @Model.RecipientName
        </td>
    </tr>
    <tr>
        <td style="vertical-align:top;">
            <a data-toggle="collapse" href="#patient-info">Patient</a>
        </td>
        <td class="col-md-12">
            <div>
                <strong>@Model.Patient.Given @Model.Patient.Family</strong>
                <div id="patient-info" class="panel-collapse collapse" style="margin-top:5px;">
                    <table class="table table-condensed table-bordered table-responsive" style="width:100%;">
                        <tr>
                            <td class="col-md-3 text-nowrap">First Name</td>
                            <td class="col-md-9">@Model.Patient.Given</td>
                        </tr>
                        <tr>
                            <td class="col-md-3 text-nowrap">Last Name</td>
                            <td class="col-md-9">@Model.Patient.Family</td>
                        </tr>
                        <tr>
                            <td class="col-md-3">DOB</td>
                            <td class="col-md-9">@Model.Patient.BirthDate.ToString("MMM dd yyyy")</td>
                        </tr>
                        <tr>
                            <td class="col-md-3">Gender</td>
                            <td class="col-md-9">@Model.Patient.Gender</td>
                        </tr>
                        @{
                            var ohip = Model.Patient.HCN + " " + Model.Patient.HCN_VC;
                            if (string.IsNullOrEmpty(ohip) || ohip == " ")
                            {
                                ohip = "Not available";
                            }
                        }
                        <tr>
                            <td class="col-md-3">HIN</td>
                            <td class="col-md-9">@ohip</td>
                        </tr>
                        @{
                            if (Model.EmrInitialRequest)
                            {
                                <tr>
                                    <td colspan="2" class="col-md-12">Initial Request: EMR</td>
                                </tr>
                            }
                            else
                            {
                                <tr>
                                    <td colspan="2" class="col-md-12">
                                         <span style="display:inline-block">
                                             <input id="txtDisassociationNote" maxlength="50" type="text" class="form-control" @btnStyleDisassociatePatient placeholder="add note here" />
                                             <input type="button" data-case-id="@Model.CaseId" class="btn btn-primary" value="Associate Patient" id="btnAssociatePatient" @btnStyleAssociatePatient />
                                             <input type="button" data-case-id="@Model.CaseId" class="btn btn-primary" value="Disassociate Patient" id="btnDisassociatePatient" @btnStyleDisassociatePatient />
                                         </span>
                                         <span style="visibility:hidden; padding-top:1px;display:inline-block;" id="span-loading-associate-disassociate-patient">
                                            <img src="~/Content/Images/loading.gif" style="width:20px;" />
                                        </span>
                                    </td>
                                </tr>
                            }
                        }
                    </table>
                </div>
            </div>
        </td>
    </tr>
    <tr>
        <td>
            Case ID
        </td>
        <td>
            @Model.CaseId
        </td>
    </tr>
    <tr>
        <td>
            Subject
        </td>
        <td>
            @Html.Truncate(Model.Title, 27, true, false)
        </td>
    </tr>
    <tr>
        <td colspan="2">
            Billing Coverage: @billingCoverage
        </td>
    </tr>
    @{  if (Model.CoverageId == 2)
        {
            <tr>
                <td colspan="2">
                    Insurance Name: @Model.IssuerName
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    Insurance Group: @Model.IssuerGroup
                </td>
            </tr>
        }
    }
</table>
@{
    if (showConsultFlag)
    {
        <table style="margin-top:30px;margin-bottom:30px;">
            <tr>
                <td>
                    <select id="ddlConsultFlag" @disabled class="form-control" data-toggle="tooltip" data-trigger="hover" data-placement="top" title="Cases can be flagged as Education, Research or Other which enables filtering by flag for easier retrieval">
                        @foreach (var item in Enum.GetValues(typeof(ConsultFlag)))
                        {
                            var _class = "consult-flag-" + CustomHelpers.GetFlagColor(item.ToString());
                            if (Model.ConsultFlag == item.ToString())
                            {
                                <option value="@item.ToString()" selected class="@_class">&#9873; @UtilityHelper.GetDescriptionFromEnumValue((ConsultFlag)item)</option>
                            }
                            else
                            {
                                <option value="@item.ToString()" class="@_class">&#9873; @UtilityHelper.GetDescriptionFromEnumValue((ConsultFlag)item)</option>
                            }
                        }
                    </select>
                </td>
            </tr>
        </table>

        <script type="text/javascript">
            $(document).ready(function () {

                $("[data-toggle=tooltip]").tooltip({
                    placement: $(this).data("placement") || 'top'
                });

                setDefaultConsultStyle();

                $("#ddlConsultFlag").change(function () {

                    var _flag = $('#ddlConsultFlag').find(':selected').val();
                    var _class = '';

                    if (_flag == "RESEARCH") {
                        _class = "consult-flag-orange";
                    }
                    else if (_flag == "EDUCATION") {
                        _class = "consult-flag-lightblue";
                    }
                    else if (_flag == "OTHER") {
                        _class = "consult-flag-lawngreen";
                    }
                    else {
                        _class = "consult-flag-black";
                    }
                    $(this).removeClass();
                    $(this).addClass('form-control').addClass(_class);
                });
            });

            function setDefaultConsultStyle() {
                var ddl = $('#ddlConsultFlag');
                var _css = ddl.find(':selected').attr('class');
                $(ddl).addClass(_css);
            }
        </script>
    }
    <p>
        <input type="button" value="Export to PDF" class="btn btn-primary" id="btnGeneratePdf" data-case-id="@Model.CaseId" />
        <span style="visibility:hidden; padding-top:1px;" id="span-loading-export-pdf">
            <img src="~/Content/Images/loading.gif" style="width:20px;" />
        </span>
        
        @if (showButtonSaveConsult)
        {
            <input type="button" value="Save Consult" class="btn btn-primary" id="btnSaveCompletedConsult" data-case-id="@Model.CaseId" />
            <span style="visibility:hidden; padding-top:1px;" id="span-loading-save-completed-consult">
                <img src="~/Content/Images/loading.gif" style="width:20px;" />
            </span>
        }
    </p>

}


