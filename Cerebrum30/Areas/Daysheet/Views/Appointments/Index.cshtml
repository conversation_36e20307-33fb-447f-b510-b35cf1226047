﻿@model IEnumerable<Cerebrum.Data.AppointmentData>
@{
    ViewBag.Title = "Dayshet";
    Layout = "~/Views/Shared/_Layout.cshtml";
    var test = Model;
}
<script src="~/Scripts/Cerebrum3-ContextMenu.js"></script>
<script src="~/Scripts/inv_daysheet.js"></script>
<link href="~/Content/cerebrum3-appointment-btns.css" rel="stylesheet" />
<style type="text/css">
    .ui-dialog .ui-dialog-title {
        margin-left: 160px !important;
    }

    .head_r {
        text-align: center;
    }

    .float_r span {
        float: right;
    }
</style>
<style type="text/css">
    input[type=search] {
        width: 230px;
        padding: 5px;
        outline: none;
        border: 2px solid #999999;
        border-radius: 5px;
        background-color: #FBFBFB;
        font-family: Cambria, Cochin, Georgia, serif;
        font-size: 16px;
        background-image: url('//www.kirupa.com/images/search.png');
        background-position: 270px -10px;
        background-repeat: no-repeat;
    }

    #contextMenu {
        position: absolute;
        display: none;
    }

    .Black {
        background-color: black;
    }

    .PatientLeftAndOrdersDone {
        background-color: cadetblue;
        text-align: center;
    }

    .FlowSheetCompleted {
        background-color: beige;
        text-align: center;
    }

    .Arrived {
        background-color: greenyellow;
        text-align: center;
    }

    .NotArrived {
        background-color: coral;
        text-align: center;
    }

    .Canceled {
        background-color: red;
        text-align: center;
    }

    .Missed {
        background-color: #7fc0c1;
        text-align: center;
    }

    .DoingTests {
        background-color: blueviolet;
        text-align: center;
    }

    .Yellow {
        background-color: Yellow;
    }

    .ReadyForDoctor {
        background-color: crimson;
        text-align: center;
    }

    .Left {
        background-color: aqua;
        text-align: center;
    }

    .btn.sharp {
        border-radius: 0;
    }

    body {
        /*background: url('../img/darkdenim3.png') repeat 0 0 #555;*/
    }
    /* DatePicker Container */
    .ui-datepicker {
        width: 216px;
        height: auto;
        margin: 38px 30px 130px;
        font: 9pt Arial, sans-serif;
        -webkit-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
        -moz-box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
        box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .5);
    }

    .ui-datepicker a {
        text-decoration: none;
    }
    /* DatePicker Table */
    .ui-datepicker table {
        width: 100%;
    }

    .ui-datepicker-header {
        /*background: url('../img/dark_leather.png') repeat 0 0 #000;*/
        color: #e0e0e0;
        font-weight: bold;
        -webkit-box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, 2);
        -moz-box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, .2);
        box-shadow: inset 0px 1px 1px 0px rgba(250, 250, 250, .2);
        text-shadow: 1px -1px 0px #000;
        filter: dropshadow(color=#000, offx=1, offy=-1);
        line-height: 30px;
        border-width: 1px 0 0 0;
        border-style: solid;
        border-color: #111;
    }

    .ui-datepicker-title {
        text-align: center;
    }

    .ui-datepicker-prev, .ui-datepicker-next {
        /*display: inline-block;
        width: 30px;
        height: 30px;
        text-align: center;
        cursor: pointer;
        background-image: url('../img/arrow.png');
        background-repeat: no-repeat;
        line-height: 600%;
        overflow: hidden;*/
    }

    .ui-datepicker-prev {
        /*float: left;
        background-position: center -30px;*/
    }

    .ui-datepicker-next {
        /*float: right;
        background-position: center 0px;*/
    }

    .ui-datepicker thead {
        background-color: #f7f7f7;
        background-image: -moz-linear-gradient(top, #f7f7f7 0%, #f1f1f1 100%);
        background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f7f7f7), color-stop(100%,#f1f1f1));
        background-image: -webkit-linear-gradient(top, #f7f7f7 0%,#f1f1f1 100%);
        background-image: -o-linear-gradient(top, #f7f7f7 0%,#f1f1f1 100%);
        /*background-image: -ms-linear-gradient(top,  #f7f7f7 0%,#f1f1f1 100%);*/
        background-image: linear-gradient(top, #f7f7f7 0%,#f1f1f1 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f7f7f7', endColorstr='#f1f1f1',GradientType=0 );
        border-bottom: 1px solid #bbb;
    }

    .ui-datepicker th {
        text-transform: uppercase;
        font-size: 6pt;
        padding: 5px 0;
        color: #666666;
        text-shadow: 1px 0px 0px #fff;
        filter: dropshadow(color=#fff, offx=1, offy=0);
    }

    .ui-datepicker tbody td {
        padding: 0;
        border-right: 1px solid #bbb;
    }

        .ui-datepicker tbody td:last-child {
            border-right: 0px;
        }

    .ui-datepicker tbody tr {
        border-bottom: 1px solid #bbb;
    }

        .ui-datepicker tbody tr:last-child {
            border-bottom: 0px;
        }

    .ui-datepicker td span, .ui-datepicker td a {
        display: inline-block;
        font-weight: bold;
        text-align: center;
        width: 30px;
        height: 30px;
        line-height: 30px;
        color: #666666;
        text-shadow: 1px 1px 0px #fff;
        filter: dropshadow(color=#fff, offx=1, offy=1);
    }

    .ui-datepicker-calendar .ui-state-default {
        background: #ededed;
        background: -moz-linear-gradient(top, #ededed 0%, #dedede 100%);
        background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ededed), color-stop(100%,#dedede));
        background: -webkit-linear-gradient(top, #ededed 0%,#dedede 100%);
        background: -o-linear-gradient(top, #ededed 0%,#dedede 100%);
        background: linear-gradient(top, #ededed 0%,#dedede 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ededed', endColorstr='#dedede',GradientType=0 );
        -webkit-box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
        -moz-box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
        box-shadow: inset 1px 1px 0px 0px rgba(250, 250, 250, .5);
    }

    .ui-datepicker-calendar .ui-state-hover {
        background: #f7f7f7;
    }

    .ui-datepicker-calendar .ui-state-active {
        background: #6eafbf;
        -webkit-box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
        -moz-box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
        box-shadow: inset 0px 0px 10px 0px rgba(0, 0, 0, .1);
        color: #e0e0e0;
        text-shadow: 0px 1px 0px #4d7a85;
        filter: dropshadow(color=#4d7a85, offx=0, offy=1);
        border: 1px solid #55838f;
        position: relative;
        margin: -1px;
    }

    .ui-datepicker-unselectable .ui-state-default {
        background: #f4f4f4;
        color: #b4b3b3;
    }

    .ui-datepicker-calendar td:first-child .ui-state-active {
        width: 29px;
        margin-left: 0;
    }

    .ui-datepicker-calendar td:last-child .ui-state-active {
        width: 29px;
        margin-right: 0;
    }

    .ui-datepicker-calendar tr:last-child .ui-state-active {
        height: 29px;
        margin-bottom: 0;
    }

    #btnAdd{
    margin-top: 18px !important;
}
</style>

<script type="text/javascript">

    var doctor = "Missing";
    $(document).ready(function () {

        //DayTime of now
        var date = new Date();
        var dofw = date.getDay();
        var day = date.getDate();
        var month = date.getMonth() + 1; //month starts with 0
        var year = date.getFullYear();
        var hour = date.getHours();
        var minute = date.getMinutes();
        var second = date.getSeconds();
        var time = month + "/" + day + "/" + year + " " + hour + ':' + minute + ':' + second;
        $('#DayTime').val(time);
        //
        $('#appDate').datepicker({
            showOn: "button",
            buttonImage: "Areas/Daysheet/Images/calendar.png",
            buttonImageOnly: true,
            inline: true,
            //nextText: '&rarr;',
            //prevText: '&larr;',
            showOtherMonths: true,
            dateFormat: "DD, d MM, yy",
            dayNamesMin: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
            yearRange: "c-7:c+7"
        });

        function GetTime(today, dateParam) {
            var param = null;
            if (today == true) {
                param = new Date();
            } else {
                param = new Date(dateParam);
            }
            var date = param,
                weekday = new Array('Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'),
                dayOfWeek = weekday[date.getDay()],
                domEnder = function () { var a = date; if (/1/.test(parseInt((a + "").charAt(0))) return "th"; a = parseInt((a + "").charAt(1)); return 1 == a ? "st" : 2 == a ? "nd" : 3 == a ? "rd" : "th" }(),
                dayOfMonth = today + (date.getDate() < 10) ? '0' + date.getDate() + domEnder : date.getDate() + domEnder,
                months = new Array('January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'),
                curMonth = months[date.getMonth()],
                curYear = date.getFullYear(),
                curHour = date.getHours() > 12 ? date.getHours() - 12 : (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()),
                curMinute = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes(),
                curSeconds = date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds(),
                curMeridiem = date.getHours() > 12 ? "PM" : "AM";
            if (today == true) {
                return curHour + ":" + curMinute + " " + curMeridiem;
            } else {
                return " " + dayOfWeek + ", " + curMonth + " " + date.getDate() + ", " + curYear;
            }
        }

        $("#appDate").change(function (event, ui) {
            window.location.href = '@Url.Action("ChangeOffice")' + '?officeId=' + $("#Offices").val() + '&Date=' + formatDate(new Date($(this).val())
        });

        $("#appointmentDateText").keydown(function (e) {
            var date = new Date($("#appointmentDateText").val());
            var day = date.getDate();
            var month = date.getMonth();
            var year = date.getFullYear();
            if (e.keyCode == 38 || e.keyCode == 40) {
                switch (e.keyCode) {
                    case 38:
                        {
                            var tomorrow = new Date(year, month, day);
                            tomorrow.setDate(tomorrow.getDate() + 1);
                            $("#appointmentDateText").val(formatDate(tomorrow));
                            window.location.href = '@Url.Action("ChangeOffice")' + '?officeId=' + $("#Offices").val() + '&Date=' + formatDate(tomorrow)
                            break;
                        }
                    case 40:
                        {
                            var yesterday = new Date(year, month, day);
                            yesterday.setDate(yesterday.getDate() - 1);
                            window.location.href = '@Url.Action("ChangeOffice")' + '?officeId=' + $("#Offices").val() + '&Date=' + formatDate(yesterday)
                            break;
                        }
                }
            }
        });


        $("#Offices").change(function (event, ui) {
            callBack($(this).val());
        });

        $("#doctor").on('input', function () {
            doctor = $(this).val();
            var officeId = $("#Offices").val();
            if (doctor == "All") {
                window.location.href = '@Url.Action("ChangeOffice")' + '?officeId=' + officeId
            } else {
                if (doctor != "Missing" && doctor.length > 10) {
                    window.location.href = '@Url.Action("ChangeOffice")' + '?officeId=' + officeId + '&doctorName=' + doctor
                }
            }
        });

        $("#patient").on('input', function () {
            var officeId = $("#Offices").val();
            var patient = $(this).val();
            if (patient == "All") {
                window.location.href = '@Url.Action("ChangeOffice")' + '?officeId=' + officeId
            } else {
                if (patient.length > 10) {
                    window.location.href = '@Url.Action("ChangeOffice")' + '?officeId=' + officeId + '&doctorName=' + '' + '&patientName=' + patient
                }
            }
        });

        $("#ForCreateOffices").change(function (event, ui) {
            //ForCreateCallBack($(this).val());
        });

        $('[id^="Arrived"]').on("dblclick", function (e) {
            var id = jQuery(this).attr("id");
            AppId = id.replace("Arrived", "");
            var timeonly = GetTime(true, null);
            $(this).val(timeonly);
            var officeId = $("#Offices").val();
            var AppDate = $("#appDate").val();
            if (AppDate == "") {
                AppDate = time;
            }
            $.ajax({
                method: 'post',
                url: '/daysheet/appointments/setArrived',
                data: { officeId: officeId, AppId: AppId }
            });
        });

        $('[id^="Left"]').on("dblclick", function (e) {
            var id = jQuery(this).attr("id");
            AppId = id.replace("Left", "");
            var timeonly = GetTime(true, null);
            $(this).val(timeonly);
            var officeId = $("#Offices").val();
            var AppDate = $("#appDate").val();
            if (AppDate == "") {
                AppDate = time;
            }
            $.ajax({
                method: 'post',
                url: '/daysheet/appointments/setLeft',
                data: { officeId: officeId, AppId: AppId }
            });
        });

        /* Room Number for Patient */
        var roomEditPK;
        $('.RoomEditable').popover({
            content: function () {
                roomEditPK = $(this).data('pk');
                var roomval = $('.RoomEditable').data('value');
                var htmlString = $("#add-popover-content").html();
                $(htmlString).find("input[id=roomId]").val(roomval);
                return htmlString;
            }, html: true
        }).on('click', function () {
            $('.btn-primary').click(function () {
                var room = $('#roomId').val();
                $.ajax({
                    method: 'post',
                    url: '/daysheet/appointments/roomNumber/',
                    data: { appointmentId: roomEditPK, roomNumber: room }
                });
                $('#RoomEditable_' + roomEditPK).html('Room # ' + room + '  <span class="glyphicon glyphicon-pencil"></span>');
                $(".RoomEditable").popover('hide');
            });
            $('.btn-default').click(function () {
                $(".RoomEditable").popover('hide');
            });
        });
    });

    function formatDate(date) {
        var hours = date.getHours();
        var minutes = date.getMinutes();
        var ampm = hours >= 12 ? 'pm' : 'am';
        hours = hours % 12;
        hours = hours ? hours : 12; // the hour '0' should be '12'
        minutes = minutes < 10 ? '0' + minutes : minutes;
        var strTime = hours + ':' + minutes + ' ' + ampm;
        var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
        return date.getMonth() + 1 + "/" + day + "/" + date.getFullYear();
    }

    /* Room Number for Patient */
    function callBack(param) {
        window.location.href = '@Url.Action("ChangeOffice")' + '?officeId=' + param
    };

    $(function () {
        $("#datepicker").datepicker();
    });

    function preConditionStatusChange(appId, preconId) {
        $.ajax({
            method: 'post',
            url: '/daysheet/appointments/PreCondtionStatusChange/',
            data: { appointmentId: appId, preconditionId: preconId }
        })
    }

    function OnAppointmentEdit(url) {
        $.ajax({
            method: 'get',
            url: url
        }).done(function (rtn) {
            $("#appointmentCollapse").html(rtn);
        });
        return false;
    }

    function ShowExpected(exall) {
        var url = window.location.href;
        var dt = $("#appointmentDateText").val();
        console.log(exall);
        var current = new Date();
        var tim = current.getHours() + ":" + current.getMinutes();
        if (exall == 'expected') {
            window.location.href = '@Url.Action("Index")' + '?Office=' + $("#Offices").val() + '&DateOfApp=' + formatDate(new Date(dt)) + '&Time=' + tim;
        } else {
            window.location.href = '@Url.Action("Index")' + '?Office=' + $("#Offices").val() + '&DateOfApp=' + formatDate(new Date(dt));
        }
    }

    function ExcludeTestOnly(ex) {
        var url = window.location.href;
        console.log(url + '  ' + ex);
        if (ex == "exclude") {
            if (url.indexOf('?') < 0) {
                window.location.href = url + '?testOnly=true';
            } else {
                window.location.href = url + "&testOnly=true";
            }
        } else {
            if (url.indexOf('&') < 0) {
                window.location.href = url.replace("?testOnly=true", "");
            } else {
                window.location.href = url.replace("&testOnly=true", "");
            }
        }
    }

    /* Show Hide Appointment*/
    function HideAppointments(appStatus) {
        $('#appointmentsTable > tbody').each(function () {
            $(this).show("slow");
        });
        var notCancelIds = [];
        $('#appointmentsTable > tbody  > tr').each(function () {
            var cnt = 0;
            $("td", this).each(function () {
                var cls = $(this).hasClass(appStatus);

                //console.log(cls);
                cnt++;
                if (!cls) {
                    var newid = $(this).closest('tbody').attr('id');
                    if (notCancelIds.indexOf(newid) < 0) {
                        notCancelIds.push(newid);
                        //console.log((cnt)+"PUSH: " + notCancelIds);
                    }
                } else {
                    var newid = $(this).closest('tbody').attr('id');
                    notCancelIds.pop();
                    //console.log((cnt) + "POP:" + notCancelIds);
                    return false;
                }

            });
        });
        notCancelIds.forEach(HideThisIds)
    }
    function HideThisIds(i) {
        //console.log('Find id:'+i);
        $('#' + i).slideUp(600);
        $('#' + i).hide();
    }
    /* Show Hide Appointment*/
</script>
<div class="panel panel-default">
    @{if (ViewBag.editFlag == false)
        {
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" href="#appointmentCollapse" id="appointmentPanelTitle">New Appointment</a>
                </h4>
            </div>
            <div class="panel-body panel-collapse collapse" id="appointmentCollapse" style="padding:5px">
                @Html.Action("Create", "Appointments")
            </div>

        }
        else
        {
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" href="#appointmentCollapse" id="appointmentPanelTitle">Edit Appointment</a>
                </h4>
            </div>
            <div class="panel-body panel-collapse" id="appointmentCollapse" style="padding:5px">
                @Html.Action("Edit", "Appointments", new { id = ViewBag.EditAppId })
            </div>}
    }
</div>



<hgroup>
    <h4>Daysheet for Practice:&nbsp;@ViewBag.PracticeName&nbsp;and&nbsp;Office:@ViewBag.OfficeName </h4>
</hgroup>
<table class="table">
    <tr>
        @{
            if (ViewBag.CriticalResources.Contains("OpenTestWorksheet_Delete"))
            {
                <td>
                    <label>Critical Resource</label>
                </td>
            }
        }
        <td>
            <label id="appDateDisplay" style="font-size:large">
                @ViewBag.AppDate.ToLongDateString()
                <input type="text" id="appointmentDateText" value="@ViewBag.AppDate.ToString("MM/dd/yyyy")" />
            </label>
        </td>
        <td><input type="hidden" id="appDate" /> </td>
        <td>&nbsp;&nbsp;</td>
        <td>
            @{List<string> members = new List<string>();
                var request = Request["doctor"];
                if (request == null) { request = "Select a Doctor"; }
            }
            <input id="doctor" type="search" list="providers" placeholder="@request" class="form-control">
            <datalist id="providers">
                <option><text>All</text></option>
                @{
                    foreach (var item in Model)
                    {
                        foreach (var seldoc in item.selectDocs)
                        {
                            if (!members.Any(str => str.Contains(seldoc)))
                            {
                                members.Add(seldoc);
                                <option>@seldoc</option>
                            }
                        }
                    }
                }
            </datalist>
        </td>
        <td>@Html.DropDownList("Offices", null, null, new { @class = "form-control" })</td>
        <td>
            @{List<string> patients = new List<string>();
                var reqpatient = Request["patient"];
                if (reqpatient == null) { reqpatient = "Select a Patient"; }
            }
            <input id="patient" type="search" list="patients" placeholder="@reqpatient" class="form-control" hidden="hidden">
            <datalist id="patients">
                <option><text>All</text></option>
                @{
                    foreach (var item in ViewBag.ListOfPats)
                    {
                        <option>@item</option>
                    }
                }
            </datalist>
        </td>
        <td>
            <button type="button" id="appointmentSwipeCard" onclick="javaScript: onSwipeCardButtonClick()" class="btn btn-primary">Swipe Card</button>
        </td>
        <td>
            <button type="button" id="appointmentExpected" class="btn btn-primary" onclick='javascript: ShowExpected(@(ViewBag.Time.Trim()==""?"'expected'":"'all'"))'>Show @(ViewBag.Time.Trim() == "" ? "Expected" : "All")</button>
        </td>
        <td>
            <button type="button" id="appointmentCancelled" class="btn btn-primary" onclick="javascript: HideAppointments('Canceled')">Show Cancelled</button>
        </td>
        <td>
            <button type="button" id="appointmentTestOnly" class="btn btn-primary" onclick="javascript: ExcludeTestOnly(@(ViewBag.TestOnly?"'all'":"'exclude'"))">@(ViewBag.TestOnly ? "Include " : "Exclude ") Tests Only</button>
        </td>
        <td>
            <button type="button" id="appointmentMissed" class="btn btn-primary" onclick="javascript: HideAppointments('Missed')">Display Missed Appts</button>
            <button type="button" id="test_test" class="btn btn-primary">H2</button>
        </td>
    </tr>
</table>
<table id="appointmentsTable" class="table">
    <tr>
        <th>
            @Html.DisplayNameFor(model => model.Time)
        </th>
        <th>            <text>Arrived/Left</text>        </th>
        <th>Tests</th>
        <th>No header</th>

        <th>
            @Html.DisplayNameFor(model => model.patientDemo.Patient)
        </th>
        <td>VP</td>
        <th>Billing</th>

        <th>
            @Html.DisplayNameFor(model => model.Notes)
        </th>



        <th>


            @Html.DisplayNameFor(model => model.ReferralDoctor)
        </th>

    </tr>
    @foreach (var item in Model)
    {
        @(await Html.PartialAsync("Appointment", item))
    }
</table>
@Scripts.Render("~/bundles/cerebrum")
<ul id="contextMenu" class="dropdown-menu" role="menu" style="display:none">
    @foreach (var t in ViewBag.AppointmentStatuses)
    {
        <li><a tabindex="-1" href="javascript:void(0)">@t</a></li>
    }
</ul>
<div id="add-popover-content" class="hidden">
    <form>
        <div class='form-group col-xs-10'>
            <input type="text" name="roomId" id="roomId" value="" autocomplete="off" />
        </div>
        <div class='form-group col-xs-4'>
            <button type='button' class='btn btn-default' data-dismiss='modal'>Close</button>
        </div><div class='form-group col-xs-4'>
            <button type='button' class='btn btn-primary'>Save</button>
        </div>
    </form>

    <div id="validation-msg"></div>
</div>


<div id="ex_signDevice">
    <div>
        <div class="form-group">
            <div class="col-md-12 head_r">
                <label class="green" id="actionId"></label>
            </div>
            <div class="form-group">
                <div class="col-md-4 float_r">
                    <span>Technologist:</span>
                </div>
                <div class="col-md-6 ">
                    <select class="form-control" id="technologistId_d"></select>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-4 float_r">
                    <span>Device ID:</span>
                </div>
                <div class="col-md-3 input_">
                    <input type="text" class="form-control" id="invCodes_d">
                    <input type="hidden" id="invCodes_hid" />
                    <input type="hidden" id="invRetDateNumDays_hid" />
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-12">
                    <label class="green" id="message"></label>
                </div>
            </div>
        </div>
    </div>
</div>
