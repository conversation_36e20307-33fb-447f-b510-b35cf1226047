﻿@model Cerebrum.ViewModels.Requisition.ExternalFormResponse

@using System.Linq;
@using Newtonsoft.Json;
@using Cerebrum30.Areas.ExternalDocument.Models;

<style type="text/css">
    input, select, textarea {
        max-width: 100%;
    }

    .test-selected {
        color: red;
    }

    .ui-dialog, .ui-widget, .ui-widget-content {
        background: white !important;
    }

    .ui-dialog-titlebar {
        background-color: steelblue;
        color: yellow;
    }        
</style>

<script type="text/javascript" src="~/Areas/Requisition/Scripts/External.js"></script>

@if (string.IsNullOrEmpty(Model.errorMessage))
{
    string[] checkboxes = null;
    TextValueViewModel[] selections = null;
    TextValueViewModel[] texts = null;
    string externalTest = string.Empty;
    TextValueViewModel testOther = new TextValueViewModel();

    if (!string.IsNullOrEmpty(Model.requisitionItems))
    {
        dynamic requisitionItems = JsonConvert.DeserializeObject<dynamic>(Model.requisitionItems);
        if (requisitionItems.checkboxes != null)
        {
            checkboxes = requisitionItems.checkboxes.ToObject<string[]>();
        }
        if (requisitionItems.selections != null)
        {
            selections = requisitionItems.selections.ToObject<TextValueViewModel[]>();
        }
        if (requisitionItems.texts != null)
        {
            texts = requisitionItems.texts.ToObject<TextValueViewModel[]>();
        }
        if (requisitionItems.test != null)
        {
            externalTest = requisitionItems.test.ToString();
            //externalTest = string.Join(",", Model.tests.Select(a => a.value).ToList());
        }
        if (requisitionItems.testOther != null)
        {
            testOther = requisitionItems.testOther.ToObject<TextValueViewModel>();
        }
    }
    <script type="text/javascript">
        var externalTemplateId = "@Model.templateId";
        var externalRequisitionId = "@Model.requisitionId";
        var criticalResources = @Html.Raw(Json.Serialize(Model.criticalResources));
        var viewData = @Model.viewData.ToString().ToLower();
        var requisitionItems = "@Model.requisitionItems";
        var isDoctor = @(Model.isDoctor? "1" : "0");
        var templateOpenedFromExternal = "@Model.template";
    </script>
    <form id="externalForm" name="externalForm">
        <div class="row">
            <div class="form-group col-sm-6">
                <label class="col-sm-2 control-label">
                    Requested
                </label>
                <div class="col-sm-8">
                    <select class="form-control externalDataArea" multiple id="requisition_time" name="requisition_time" style="height: 72px;">
                        @{
                            foreach (var rt in Model.times)
                            {
                                <option value="@rt.value" @selectedStatus("requisition_time", rt.value, selections)>@rt.text</option>
                            }
                        }
                    </select>
                </div>
            </div>
            <div class="form-group col-sm-6">
                <div class="form-group col-sm-2"></div>
                <div class="form-group col-sm-10">
                    <div class="form-group col-sm-12">
                        <label class="col-sm-6 control-label">
                            To be done at
                        </label>
                        <div class="col-sm-6">
                            <input type="text" id="toBeDone" name="toBeDone" class="form-control clearable externalDataArea" value="@selectedValue("toBeDone", texts)" />
                        </div>
                    </div>
                    <div class="form-group col-sm-12">
                        <label class="col-sm-6 control-label">
                            Action on Abnormal
                        </label>
                        <div class="col-sm-6">
                            <input type="checkbox" class="externalDataArea" value="actionOnAbnormal" @checkboxStatus("actionOnAbnormal", checkboxes) />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="col-sm-12">
            <b>For office use only:</b>
           <table class="text-center" style="width: 100%; border: 1px solid #dddddd;">
                <tr>
                    <td style="border-right: 1px solid #dddddd; height: 72px; vertical-align: top;">
                        Date requisition received
                    </td>
                    <td style="vertical-align: top;">
                        Scheduled Exam Date
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-sm-12 row">
            <br />
            <div class="form-group col-sm-7">
                <b>Patient Name: </b><span id="patientName" name="patientName" class="externalDataArea">@selectedValue("patientName", texts)</span> 
                <b>D.O.B. </b><span id="patientDOB" name="patientDOB" class="externalDataArea">@selectedValue("patientDOB", texts)</span>  <br />
                <b>Address: </b><br /><span id="patientAddress" name="patientAddress" class="externalDataArea">@selectedValue("patientAddress", texts)  </span> 
                <b>M/F </b> <span id="patientGender" name="patientGender" class="externalDataArea">@selectedValue("patientGender", texts)</span><br />
                <b>Home #: </b> <span id="patientHomePhone" name="patientHomePhone" class="externalDataArea">@selectedValue("patientHomePhone", texts)</span>   
                <b>Work #: <span id="patientWorkPhone" name="patientWorkPhone" class="externalDataArea">@selectedValue("patientWorkPhone", texts)</span></b> 
                <b>Healthcard #: </b> <span id="patientHealthCard" name="patientHealthCard" class="externalDataArea">@selectedValue("patientHealthCard", texts)</span><br /><br />
                <b>Referring Physician: </b> <span id="doctorName" name="doctorName" class="externalDataArea">@selectedValue("doctorName", texts)</span>  
                <b>Signature: </b><img src="@selectedValue("doctorSignature", texts)" style="position:relative;left:8px;top:0px;width: 88px; height:28px; border-width: 0 0 0 0; border-style: solid;" />
            </div>
            <div class="form-group col-sm-5">
                <b>Clinical or Relevant History</b>
                <textarea id="clinicalHistory" name="clinicalHistory" rows="4" class="form-control externalDataArea" style="height: auto; width: 100%;">@selectedValue("clinicalHistory", texts)</textarea>
            </div>
        </div>
        <div class="col-sm-12">
            <br />
            <div class="text-center"><b>Requested Exams</b></div>           
            <table style="width: 100%; border: 1px solid #dddddd;" id="requestedExams" name="requestedExams">
                <tr>
                    <td valign="top" style="border-right: 1px solid #dddddd; padding-left: 8px;">
                        <table border="0" style="vertical-align:top;">
                            <tr>
                                <td colspan="2">
                                    <b>CT</b>
                                </td>
                            </tr>
                            <tr id="r6" class="@checkboxClass("6", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="6" @checkboxStatus("6", externalTest)> </td>
                                <td>Head</td>
                            </tr>
                            <tr id="r7" class="@checkboxClass("7", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="7" @checkboxStatus("7", externalTest)> </td>
                                <td>Neck</td>
                            </tr>
                            <tr id="r13" class="@checkboxClass("13", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="13" @checkboxStatus("13", externalTest)> </td>
                                <td>Chest</td>
                            </tr>
                            <tr id="r8" class="@checkboxClass("8", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="8" @checkboxStatus("8", externalTest)> </td>
                                <td>Abdomen</td>
                            </tr>
                            <tr id="r9" class="@checkboxClass("9", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="9" @checkboxStatus("9", externalTest)> </td>
                                <td>Pelvis</td>
                            </tr>
                            <tr id="r10" class="@checkboxClass("10", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="10" @checkboxStatus("10", externalTest)> </td>
                                <td>Spine Cervical</td>
                            </tr>
                            <tr id="r11" class="@checkboxClass("11", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="11" @checkboxStatus("11", externalTest)> </td>
                                <td>Spine Lumbar</td>
                            </tr>
                            <tr id="r12" class="@checkboxClass("12", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="12" @checkboxStatus("12", externalTest)> </td>
                                <td>Extremety</td>
                            </tr>
                            <tr id="r66" class="@checkboxClass("66", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="66" @checkboxStatus("66", externalTest)> </td>
                                <td>CT Colonography</td>
                            </tr>
                            <tr id="r67" class="@checkboxClass("67", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="67" @checkboxStatus("67", externalTest)> </td>
                                <td>CT Angiogram</td>
                            </tr>

                            <tr id="r14" class="@checkboxClass("14", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="14" @checkboxStatus("14", externalTest)> </td>
                                <td>Carotid</td>
                            </tr>
                            <tr id="r15" class="@checkboxClass("15", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="15" @checkboxStatus("15", externalTest)> </td>
                                <td>Pulmonary</td>
                            </tr>
                            <tr id="r16" class="@checkboxClass("16", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="16" @checkboxStatus("16", externalTest)> </td>
                                <td>Aorta</td>
                            </tr>
                            <tr><td colspan="2">Other<br><input type="text" name="other1" id="other1" value="@selectedValue("other1", testOther)"></td></tr>
                            <tr>
                                <td colspan="2">
                                    <b>MRI</b>
                                </td>
                            </tr>
                            <tr id="r86" class="@checkboxClass("86", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="86" @checkboxStatus("86", externalTest)> </td>
                                <td>Cardiac</td>
                            </tr>
                            <tr id="r87" class="@checkboxClass("87", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="87" @checkboxStatus("87", externalTest)> </td>
                                <td>Spine</td>
                            </tr>
                            <tr><td colspan="2">Other<br><input type="text" name="other7" id="other7" value="@selectedValue("other7", testOther)"></td></tr>
                        </table>  
                    </td>
                    <td valign="top" style="border-right: 1px solid #dddddd; padding-left: 8px;">
                        <table border="0" style="">
                            <tr>
                                <td colspan="2">
                                    <b>Ultrasound</b>
                                </td>
                            </tr>
                            <tr id="r17" class="@checkboxClass("17", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="17" @checkboxStatus("17", externalTest)> </td>
                                <td>Abdomen</td>
                            </tr>
                            <tr id="r18" class="@checkboxClass("18", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="18" @checkboxStatus("18", externalTest)> </td>
                                <td>Pelvis</td>
                            </tr>
                            <tr id="r19" class="@checkboxClass("19", externalTest)">
                                <td><input type="checkbox"  onclick="externalCheckboxClick(this);" value="19" @checkboxStatus("19", externalTest)> </td>
                                <td>Pelvis TV</td>
                            </tr>
                            <tr id="r20" class="@checkboxClass("20", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="20" @checkboxStatus("20", externalTest)> </td>
                                <td>Transrectal Prostate</td>
                            </tr>
                            <tr id="r21" class="@checkboxClass("21", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="21" @checkboxStatus("21", externalTest)> </td>
                                <td>Obstetrical Thyroid</td>
                            </tr>
                            <tr id="r22" class="@checkboxClass("22", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="22" @checkboxStatus("22", externalTest)> </td>
                                <td>Scrotum</td>
                            </tr>
                            <tr id="r23" class="@checkboxClass("23", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="23" @checkboxStatus("23", externalTest)> </td>
                                <td>Arterial: Carotid</td>
                            </tr>
                            <tr id="r24" class="@checkboxClass("24", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="24" @checkboxStatus("24", externalTest)> </td>
                                <td>Leg</td>
                            </tr>
                            <tr id="r25" class="@checkboxClass("25", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="25" @checkboxStatus("25", externalTest)> </td>
                                <td>Venous: Leg</td>
                            </tr>
                            <tr id="r26" class="@checkboxClass("26", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="26" @checkboxStatus("26", externalTest)> </td>
                                <td>msk: Knee</td>
                            </tr>
                            <tr id="r27" class="@checkboxClass("27", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="27" @checkboxStatus("27", externalTest)> </td>
                                <td>msk:Shoulder</td>
                            </tr>
                            <tr id="r28" class="@checkboxClass("28", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="28" @checkboxStatus("28", externalTest)> </td>
                                <td>Hysterosonogram</td>
                            </tr>
                            <tr id="r81" class="@checkboxClass("81", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="81" @checkboxStatus("81", externalTest)> </td>
                                <td>Renal Artery Doppler</td>
                            </tr>
                            <tr id="r88" class="@checkboxClass("88", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="88" @checkboxStatus("88", externalTest)> </td>
                                <td>Thyroid</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    Other<br>
                                    <input type="text" id="other3" name="other3" value="@selectedValue("other3", testOther)">
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td valign="top" style="border-right: 1px solid #dddddd; padding-left: 8px;">
                        <table border="0">
                            <tr>
                                <td colspan="2">
                                    <b>Nuclear Medicine</b>
                                </td>
                            </tr>
                            <tr id="r29" class="@checkboxClass("29", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="29" @checkboxStatus("29", externalTest)> </td>
                                <td>Bone</td>
                            </tr>
                            <tr id="r30" class="@checkboxClass("30", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="30" @checkboxStatus("30", externalTest)> </td>
                                <td>Brain</td>
                            </tr>
                            <tr id="r31" class="@checkboxClass("31", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="31" @checkboxStatus("31", externalTest)> </td>
                                <td>Gallium</td>
                            </tr>
                            <tr id="r32" class="@checkboxClass("32", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="32" @checkboxStatus("32", externalTest)> </td>
                                <td>Liver:rbc</td>
                            </tr>
                            <tr id="r33" class="@checkboxClass("33", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="33" @checkboxStatus("33", externalTest)> </td>
                                <td>Liver/Spleen</td>
                            </tr>
                            <tr id="r34" class="@checkboxClass("34", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="34" @checkboxStatus("34", externalTest)> </td>
                                <td>Lung: V/Q</td>
                            </tr>
                            <tr id="r35" class="@checkboxClass("35", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="35" @checkboxStatus("35", externalTest)> </td>
                                <td>Renal:Baseline</td>
                            </tr>
                            <tr id="r36" class="@checkboxClass("36", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="36" @checkboxStatus("36", externalTest)> </td>
                                <td>Renal:Captopril Thyroid</td>
                            </tr>
                            <tr id="r68" class="@checkboxClass("68", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="68" @checkboxStatus("68", externalTest)> </td>
                                <td>Bone Mineral Densitometry</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    Other<br>
                                    <input type="text" name="other5" id="other5" value="@selectedValue("other5", testOther)">
                                </td>
                            </tr>
                            <tr id="r84" class="@checkboxClass("84", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="84" @checkboxStatus("84", externalTest)> </td>
                                <td>Consultation</td>
                            </tr>
                            <tr id="r85" class="@checkboxClass("85", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="85" @checkboxStatus("85", externalTest)> </td>
                                <td>Miscellaneous</td>
                            </tr>
                            <tr id="r90" class="@checkboxClass("90", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="90" @checkboxStatus("90", externalTest)> </td>
                                <td>Pharmacy</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <b>Respiratory</b>
                                </td>
                            </tr>
                            <tr id="r69" class="@checkboxClass("69", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="69" @checkboxStatus("69", externalTest)> </td>
                                <td>Sleep Study</td>
                            </tr>
                            <tr id="r70" class="@checkboxClass("70", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="70" @checkboxStatus("70", externalTest)> </td>
                                <td>PFT</td>
                            </tr>
                            <tr id="r89" class="@checkboxClass("89", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="89" @checkboxStatus("89", externalTest)> </td>
                                <td>Resp Misc</td>
                            </tr>
                        </table>
                        <br />
                    </td>
                    <td valign="top" style="border-right: 1px solid #dddddd; padding-left: 8px;">
                        <table border="0">
                            <tr>
                                <td colspan="2">
                                    <b>Cardiac Imaging</b>
                                </td>
                            </tr>
                            <tr><td colspan="2"><b>Nuclear Stress:</b> </td></tr>
                            <tr id="r37" class="@checkboxClass("37", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="37" @checkboxStatus("37", externalTest)> </td>
                                <td>Exercise</td>
                            </tr>
                            <tr id="r38" class="@checkboxClass("38", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="38" @checkboxStatus("38", externalTest)> </td>
                                <td>Persantine</td>
                            </tr>
                            <tr id="r39" class="@checkboxClass("39", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="39" @checkboxStatus("39", externalTest)> </td>
                                <td>Rest Thallium</td>
                            </tr>
                            <tr id="r40" class="@checkboxClass("40", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="40" @checkboxStatus("40", externalTest)> </td>
                                <td>Rest MUGA</td>
                            </tr>
                            <tr><td colspan="2"><b>Echocardiography:</b> </td></tr>
                            <tr id="r41" class="@checkboxClass("41", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="41" @checkboxStatus("41", externalTest)> </td>
                                <td>Regular</td>
                            </tr>
                            <tr id="r42" class="@checkboxClass("42", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="42" @checkboxStatus("42", externalTest)> </td>
                                <td>Stress</td>
                            </tr>
                            <tr id="r43" class="@checkboxClass("43", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="43" @checkboxStatus("43", externalTest)> </td>
                                <td>Transesophageal</td>
                            </tr>
                            <tr id="r77" class="@checkboxClass("77", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="77" @checkboxStatus("77", externalTest)> </td>
                                <td>EEG</td>
                            </tr>
                            <tr id="r71" class="@checkboxClass("71", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="71" @checkboxStatus("71", externalTest)> </td>
                                <td>EP Study</td>
                            </tr>
                            <tr id="r72" class="@checkboxClass("72", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="72" @checkboxStatus("72", externalTest)> </td>
                                <td>ICD</td>
                            </tr>
                            <tr id="r73" class="@checkboxClass("73", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="73" @checkboxStatus("73", externalTest)> </td>
                                <td>Pace maker</td>
                            </tr>
                            <tr id="r74" class="@checkboxClass("74", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="74" @checkboxStatus("74", externalTest)> </td>
                                <td>Ablation</td>
                            </tr>
                            <tr id="r75" class="@checkboxClass("75", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="75" @checkboxStatus("75", externalTest)> </td>
                                <td>Cardiac MRI</td>
                            </tr>
                            <tr id="r79" class="@checkboxClass("79", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="79" @checkboxStatus("79", externalTest)> </td>
                                <td>Cardioversion</td>
                            <tr id="r80" class="@checkboxClass("80", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="80" @checkboxStatus("80", externalTest)> </td>
                                <td>Drug cost assist forms</td>
                            </tr>
                            <tr id="r76" class="@checkboxClass("76", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="76" @checkboxStatus("76", externalTest)> </td>
                                <td nowrap>Coronary Angiography</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    Other<br>
                                    <input type="text" name="other6" id="other6" value="@selectedValue("other6", testOther)">
                                </td>
                            </tr>
                            <tr id="r82" class="@checkboxClass("82", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="82" @checkboxStatus("82", externalTest)> </td>
                                <td>M-cards</td>
                            </tr>
                            <tr id="r83" class="@checkboxClass("83", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="83" @checkboxStatus("83", externalTest)> </td>
                                <td>30 day monitoring</td>
                            </tr>
                        </table>
                    </td>
                    <td valign="top" style="border-right: 1px solid #dddddd; padding-left: 8px;">
                        <table border="0">
                            <tr>
                                <td colspan="2">
                                    <b>Breast Imaging</b>
                                </td>
                            </tr>
                            <tr><td colspan="2"><b>Mammography:</b> </td></tr>
                            <tr id="r44" class="@checkboxClass("44", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="44" @checkboxStatus("44", externalTest)> </td>
                                <td>Screening</td>
                            </tr>
                            <tr id="r45" class="@checkboxClass("45", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="45" @checkboxStatus("45", externalTest)> </td>
                                <td>Workup</td>
                            </tr>
                            <tr><td colspan="2"><hr width="60%"> </td></tr>
                            <tr><td colspan="2"><b>Breast Ultrasound:</b> </td></tr>
                            <tr id="r46" class="@checkboxClass("46", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="46" @checkboxStatus("46", externalTest)> </td>
                                <td>Right</td>
                            </tr>
                            <tr id="r47" class="@checkboxClass("47", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="47" @checkboxStatus("47", externalTest)> </td>
                                <td>Left</td>
                            </tr>
                            <tr><td colspan="2"><hr width="60%"> </td></tr>
                            <tr><td colspan="2"><b>Fluoroscopy:</b> </td></tr>
                            <tr id="r48" class="@checkboxClass("48", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="48" @checkboxStatus("48", externalTest)> </td>
                                <td>Esophagus only</td>
                            </tr>
                            <tr id="r49" class="@checkboxClass("49", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="49" @checkboxStatus("49", externalTest)> </td>
                                <td>Upper GI Series</td>
                            </tr>
                            <tr id="r50" class="@checkboxClass("50", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="50" @checkboxStatus("50", externalTest)> </td>
                                <td>Small Bowel</td>
                            </tr>
                            <tr id="r51" class="@checkboxClass("51", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="51" @checkboxStatus("51", externalTest)> </td>
                                <td>Barium Enema</td>
                            </tr>
                            <tr id="r52" class="@checkboxClass("52", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="52" @checkboxStatus("52", externalTest)> </td>
                                <td>Hysterosalpingogram</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    Other<br>
                                    <input type="text" name="other4" id="other4" value="@selectedValue("other4", testOther)">
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td valign="top" style="border-right: 1px solid #dddddd; padding-left: 8px;">
                        <table border="0">
                            <tr>
                                <td colspan="2">
                                    <b>X-ray</b>
                                </td>
                            </tr>
                            <tr id="r53" class="@checkboxClass("53", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="53" @checkboxStatus("53", externalTest)> </td>
                                <td>Chest</td>
                            </tr>
                            <tr id="r54" class="@checkboxClass("54", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="54" @checkboxStatus("54", externalTest)> </td>
                                <td>Abdomen</td>
                            </tr>
                            <tr id="r55" class="@checkboxClass("55", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="55" @checkboxStatus("55", externalTest)> </td>
                                <td>Pelvis</td>
                            </tr>
                            <tr id="r56" class="@checkboxClass("56", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="56" @checkboxStatus("56", externalTest)> </td>
                                <td>Shoulder</td>
                            </tr>
                            <tr id="r57" class="@checkboxClass("57", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="57" @checkboxStatus("57", externalTest)> </td>
                                <td>Hand</td>
                            </tr>
                            <tr id="r58" class="@checkboxClass("58", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="58" @checkboxStatus("58", externalTest)> </td>
                                <td>Wrist</td>
                            </tr>
                            <tr id="r59" class="@checkboxClass("59", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="59" @checkboxStatus("59", externalTest)> </td>
                                <td>Hip</td>
                            </tr>
                            <tr id="r60" class="@checkboxClass("60", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="60" @checkboxStatus("60", externalTest)> </td>
                                <td>Knee</td>
                            </tr>
                            <tr id="r61" class="@checkboxClass("61", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="61" @checkboxStatus("61", externalTest)> </td>
                                <td>Ankle</td>
                            </tr>
                            <tr id="r62" class="@checkboxClass("62", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="62" @checkboxStatus("62", externalTest)> </td>
                                <td>Foot</td>
                            </tr>
                            <tr id="r63" class="@checkboxClass("63", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="63" @checkboxStatus("63", externalTest)> </td>
                                <td>Spine Servical</td>
                            </tr>
                            <tr id="r64" class="@checkboxClass("64", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="64" @checkboxStatus("64", externalTest)> </td>
                                <td>Spine Thoracic</td>
                            </tr>
                            <tr id="r65" class="@checkboxClass("65", externalTest)">
                                <td><input type="checkbox" onclick="externalCheckboxClick(this);" value="65" @checkboxStatus("65", externalTest)> </td>
                                <td>Spine Lumbar</td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    Other<br>
                                    <input type="text" name="other2" id="other2" value="@selectedValue("other2", testOther)">
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
        <br />
        <div class="col-sm-12 row">
            <div class="form-group col-sm-2 text-right">
                <input type="checkbox" value="iPrefer" class="externalDataArea" @checkboxStatus("iPrefer", checkboxes) />
            </div>
            <div class="form-group  col-sm-10">
                I prefer that the Radiologist schedule any additional required examinations related to current investigation on my behalf
            </div>
        </div>
        <div class="col-sm-12">
            <div class="col-sm-5 text-center">
                <input type="button" value="Save" id="externalButtonSave" name="externalButtonSave" class="btn btn-info" onclick="externalSaveClick();" />
            </div>
            <div class="col-sm-7 text-center">
                <input type="button" value="Save As Template" id="externalButtonSaveTemplate" name="externalButtonSaveTemplate" class="btn btn-info" onclick="externalSaveTemplateClick();" />
            </div>
            @*<div class="col-sm-4 text-center">
                <input type="button" value="Print View" class="btn btn-info" onclick="externalPrintClick();" />
            </div>*@
        </div>
        <input type="hidden" name="practiceDoctorId" id="practiceDoctorId" value="@selectedValue("practiceDoctorId", texts)">
    </form>
}
else
{
    <div class="text-center text-danger">
        <br /><br /><br /><br /><br /><br /><br /><br />
        <h1>@Model.errorMessage</h1>
        <br /><br /><br /><br /><br /><br /><br /><br />
    </div>
}

@functions {
    string checkboxStatus(string id, string[] checkboxesChecked)
    {
        if (checkboxesChecked == null || !checkboxesChecked.Contains(id))
        {
            return string.Empty;
        }

        return "checked";
    }

    string checkboxStatus(string id, string externalTest)
    {
        if (string.IsNullOrEmpty(externalTest) || !(("," + externalTest + ",").Contains("," + id + ",")))
        {
            return string.Empty;
        }

        return "checked";
    }

    string checkboxClass(string id, string externalTest)
    {
        if (string.IsNullOrEmpty(externalTest) || !(("," + externalTest + ",").Contains("," + id + ",")))
        {
            return string.Empty;
        }

        return "test-selected";
    }

    string selectedValue(string id, TextValueViewModel selection)
    {
        TextValueViewModel[] selections = new TextValueViewModel[] { selection };
        return selectedValue(id, selections);
    }

    string selectedValue(string id, TextValueViewModel[] selections)
    {
        if (selections == null)
        {
            return string.Empty;
        }
        var selection = selections.Where(s => s.value == id).FirstOrDefault();
        if (selection == null)
        {
            return string.Empty;
        }
        return selection.text;
    }

    string selectedStatus(string id,  string value, TextValueViewModel[] selections)
    {
        if (selections == null)
        {
            return string.Empty;
        }
        var selection = selections.Where(s => s.value == id && s.text == value).FirstOrDefault();
        if (selection == null)
        {
            return string.Empty;
        }
        return "selected";
    }
}