﻿@model Cerebrum.ViewModels.Requisition.InternalFormResponse

@using System.Linq;
@using Newtonsoft.Json;
@using Cerebrum30.Areas.ExternalDocument.Models;

<style type="text/css">
    #internalForm textarea {
        max-width: 100%;
    }
    .test-selected {
        color: red;
    }
    .spanRed {
        color: red !important;
    }
    .spanGreen {
        color: #6d9c45 !important;
    }
    .spanBlue {
        color: #6464ec !important;
    }
</style>

<script type="text/javascript" src="~/Areas/Requisition/Scripts/Internal.js"></script>

@if (string.IsNullOrEmpty(Model.errorMessage))
{
    string[] checkboxes = null;
    TextValueViewModel[] selections = null;
    TextValueViewModel[] texts = null;
    if (!string.IsNullOrEmpty(Model.requisitionItems))
    {
        dynamic requisitionItems = JsonConvert.DeserializeObject<dynamic>(Model.requisitionItems);
        checkboxes = requisitionItems.checkboxes.ToObject<string[]>();
        selections = requisitionItems.selections.ToObject<TextValueViewModel[]>();
        texts = requisitionItems.texts.ToObject<TextValueViewModel[]>();
    }
    <script type="text/javascript">
        var internalTemplateId = "@Model.templateId";
        var internalRequisitionPatientId = "@Model.requisitionPatientId";
        var internalRequisitionId = "@Model.requisitionId";
        var internalPracticeId = "@Model.practiceId";
        var internalPatientRecordId = "@Model.patientRecordId";
        var criticalResources = @Html.Raw(Json.Serialize(Model.criticalResources));
        var viewData = @Model.viewData.ToString().ToLower();
        var internalBookDates = @Html.Raw(Json.Serialize(Model.bookDates));
        var isDoctor = @(Model.isDoctor? "1" : "0");
        var templateOpenedFromInternal = "@Model.template";
    </script>
    <form id="internalForm" name="internalForm">
        <div class="form-group col-sm-12">
            <b>Patient Name: </b><span style="margin-right: 32px;">@Model.patientName</span>
            <b>D.O.B. </b><span style="margin-right: 32px;">@Model.patientDOB</span>
            <b>Healthcard #: </b><span style="margin-right: 32px;">@Model.healthCard</span>
            <b>Telephone #: </b><span>@Model.phone</span>
        </div>
        <br /><br />
        <div class="row">
            <div class="form-group col-sm-6">
                <label class="col-sm-2 control-label">
                    Office
                </label>
                <div class="col-sm-10">
                    <select class="form-control " id="officeId" name="officeId" onchange="dataChange()">
                        @{
                            string optionSelected = selectedValue("officeId", selections);
                            string selectedOfficeId = optionSelected;
                            foreach (var office in Model.offices)
                            {
                                <option value="@office.value" @selectedStatus(office.value, optionSelected)> @office.text </option>
                            }
                        }
                    </select>
                </div>
            </div>
            <div class="form-group col-sm-6">
                <label class="col-sm-3 control-label">
                    Kind of
                </label>
                <div class="col-sm-9">
                    <select class="form-control " id="kindOfId" name="kindOfId" onchange="dataChange()">
                        @{
                            optionSelected = selectedValue("kindOfId", selections);
                            foreach (var ko in Model.kindOfs)
                            {
                                <option value="@ko.value" @selectedStatus(ko.value, optionSelected)>@ko.text</option>
                            }
                        }
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group col-sm-6">
                <label class="col-sm-2 control-label">
                    Doctor
                </label>
                <div class="col-sm-10">
                    <select class="form-control " id="practiceDoctorId" name="practiceDoctorId" onchange="internalTestDoctorChange()">
                        @{
                            optionSelected = selectedValue("practiceDoctorId", selections);
                            string selectedPracticeDoctorId = optionSelected;
                            foreach (var doctor in Model.doctors)
                            {
                                <option value="@doctor.value" @selectedStatus(doctor.value, optionSelected)>@doctor.text</option>
                            }
                        }
                    </select>
                </div>
            </div>
            <div class="form-group col-sm-6" style="margin-top: 8px;">
                <label class="col-sm-3 control-label">
                    Electronically Signed
                </label>
                <div class="col-sm-3">
                    <img id="internalTestSignature" class="" src="" style="width: 80px; height:24px; border-width: 0 0 0 0; border-style: solid;" />
                </div>
                <div class="form-group col-sm-6">
                    <label class="col-sm-5 control-label">
                        Date Signed
                    </label>
                    <div class="col-sm-7">
                        <input type="text" value="@selectedValue("signatureDate", texts)" class="form-control input-sm" id="signatureDate" name="signatureDate">
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <label class="col-sm-2 control-label">
                    Indication
                </label>
                <div class="col-sm-10">
                    <textarea id="description" name="description" required rows="3" class="form-control" style="height: auto;" onchange="dataChange()">@selectedValue("description", texts)</textarea>
                </div>
            </div>
            <div class="form-group col-sm-6">
                <label class="col-sm-3 control-label">
                    Action on Abnormal
                </label>
                <div class="col-sm-9">
                    <input type="checkbox" id="actionOnAbnormalId" value="actionOnAbnormal" @checkboxStatus("actionOnAbnormal", checkboxes) onchange="dataChange()">
                </div>
            </div>
        </div>
        <div class="col-sm-12" style="margin-top: 16px;">
            <div class="panel panel-default">
                <div class="panel-heading">Internal Test</div>
                <div class="panel-body">
                    <div>
                        <input id="reportPhraseId" name="reportPhraseId" type="hidden" value="" />
                        <table width="100%" id="internalTestList">
                            <tbody>
                                @for (int i = 1; i < Model.tests.Count; i++)
                                {
                                    if (Model.tests[i].value == "29")
                                    {
                                        var testVisitPage = Model.tests[i];
                                        Model.tests.RemoveAt(i);
                                        Model.tests.Insert(0, testVisitPage);
                                        break;
                                    }
                                }
                                @for (int i = 0; i < (Model.tests.Count + 1) / 2; i++)
                                {
                                    <tr style="height: 40px;">
                                        @for (int j = 0; j < 2; j++)
                                        {
                                            int m = (Model.tests.Count + 1) / 2;
                                            if (i + j * m < Model.tests.Count)
                                            {
                                                var test = Model.tests[i + j * m];
                                                string checkboxChecked = checkboxStatus(test.value, checkboxes);
                                                string textClass = string.IsNullOrEmpty(checkboxChecked) ? string.Empty : "test-selected";
                                                optionSelected = selectedValue(test.value + "_time", selections);
                                                string paddingRight = "32px; ";
                                                if (j == 1)
                                                {
                                                    paddingRight = "0px; ";
                                                }

                                                string bookDisabled = string.Empty;
                                                string reasonDisabled = string.Empty;
                                                if (string.IsNullOrEmpty(checkboxChecked))
                                                {
                                                    bookDisabled = "disabled";
                                                    reasonDisabled = "disabled";
                                                }
                                                string disabledButton = string.Empty;
                                                if (Model.disabledBookTestIds.Contains(int.Parse(test.value)))
                                                {
                                                    bookDisabled = "disabled";
                                                    disabledButton = "1";
                                                }
                                                bool isVP = false;
                                                if (test.text.Trim().ToLower().Replace(" ", "").Contains("followupwithdoctor"))
                                                {
                                                    isVP = true;
                                                }
                                                int appointmentId = 0;
                                                var bookedTest = Model.bookedTestIds.Where(a => a.testId == int.Parse(test.value)).FirstOrDefault();
                                                if (bookedTest != null)
                                                {
                                                    appointmentId = bookedTest.appointmentId;
                                                }
                                                <td>
                                                    <input type="checkbox" id="@test.value" name="@test.value" class="testDataArea" onclick='testClick("@test.value", "@test.text")' value="@test.value" @checkboxChecked>
                                                </td>
                                                <td id='@(@test.value + "_text")' name='@(@test.value + "_text")' style="padding-left: 12px; padding-right: 12px;" class="@textClass">
                                                    @test.text
                                                </td>
                                                <td>
                                                    <select id='@(@test.value + "_time")' name='@(@test.value + "_time")' onchange='timeChange("@test.value", "@test.text")'>
                                                        <option value="">Select</option>
                                                        @foreach (var rt in Model.times)
                                                        {
                                                            <option value="@rt.value" @selectedStatus(rt.value, optionSelected)>@rt.text</option>
                                                        }
                                                    </select>
                                                </td>
                                                @*<td>
                                                    @if (!isVP)
                                                    {
                                                        <span id='@(@test.value + "_reason_span")' name='@(@test.value + "_reason_span")' style='display: @(string.IsNullOrWhiteSpace(reasonDisabled) ? "none" : "block");'>Reason ...</span>
                                                        <a id='@(@test.value + "_reason_link")' name='@(@test.value + "_reason_link")' onclick='reasonClick(@test.value, @appointmentId, @Model.requisitionId);' style='display: @(string.IsNullOrWhiteSpace(reasonDisabled) ? "block" : "none");'>Reason ...</a>
                                                    }
                                                    <input id='@(@test.value + "_reason")' name='@(@test.value + "_reason")' type="hidden" value="" />
                                                </td>*@
                                                <td style="padding-right: @paddingRight">
                                                    <input type="button" value="Book" id='@(@test.value + "_book")' class="btn btn-info" @bookDisabled data-disabled-button="@disabledButton" onclick="bookClick(@test.value)" />
                                                </td>
                                            }
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <br />
        <div class="col-sm-12 hidden-print" style="margin-top: 16px;">
            <div class="col-sm-4 text-center">
                <input type="button" id="internalButtonSave" name="internalButtonSave" value="Save" class="btn btn-info" onclick="saveClick('');" />
            </div>
            <div class="col-sm-4 text-center">
                <input type="button" id="internalButtonPrint" name="internalButtonPrint" value="Save & Print" class="btn btn-info" onclick="printClick('');" />
            </div>
            <div class="col-sm-4 text-center">
                <input type="button" id="internalButtonSaveAs" name="internalButtonSaveAs" value="Save As Template" class="btn btn-info" onclick="saveTemplateClick();" />
                <input type="button" value="Book" id="internalButtonAppoint" class="btn btn-info btn-sch-new-appointment" style="display: none;"
                       data-resource-type="practicedoctorid" data-resource-id="@selectedPracticeDoctorId" data-action-on-abnormal="false"
                       data-practice-id="@Model.practiceId" data-office-id="@selectedOfficeId" data-tests="0" data-requisition-id="@Model.requisitionId"
                       data-patient-record-id="@Model.patientRecordId" data-patient-name="@Model.patientName" data-appointment-day="@DateTime.Now.ToString("MM/dd/yyyy")" />
            </div>
        </div>
    </form>
    <div id="imageLoading" name="imageLoading" style="position: fixed; left: 30%; top: 40%; display: none;">
        <img src="@Url.Content("~/Content/Images/ajax-loader.gif")" />
    </div>
}
else
{
    <div class="text-center text-danger">
        <br /><br /><br /><br /><br /><br /><br /><br />
        <h1>@Model.errorMessage</h1>
        <br /><br /><br /><br /><br /><br /><br /><br />
    </div>
}

@functions {
    string checkboxStatus(string id, string[] checkboxesChecked)
    {
        if (checkboxesChecked == null || !checkboxesChecked.Contains(id))
        {
            return string.Empty;
        }
        return "checked";
    }

    string selectedValue(string id, TextValueViewModel[] selections)
    {
        if (selections == null)
        {
            return string.Empty;
        }
        var selection = selections.Where(s => s.value == id).FirstOrDefault();
        if (selection == null)
        {
            return string.Empty;
        }
        return selection.text;
    }

    string selectedStatus(string value, string selectedValue)
    {
        return (value == selectedValue ? "selected" : string.Empty);
    }
}