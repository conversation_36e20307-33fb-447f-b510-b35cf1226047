﻿@model Cerebrum.ViewModels.OLIS.VMOlisReport
@using Cerebrum30.Utility;
<div style="width: 100%;" >
    <div class="btn-group">
        <a class="btn btn-primary" data-toggle="collapse" href="#<EMAIL>">Patient info</a> 
        <a class="btn btn-primary" data-toggle="collapse" href="#<EMAIL>">Report/Order Note</a>
        <a class="btn btn-primary" data-toggle="collapse" href=".request-specimen-collapse">Specimen</a>
        <a class="btn btn-primary  btn-category-sort" data-btn-value="tc-category">Sort by Category Name</a>                    
    </div>
    <div id="<EMAIL>" class="row panel-collapse collapse">
        <div class="col-sm-4"> @(await Html.PartialAsync("Patient", Model.Patient))</div>
        <div class="col-sm-4"> @(await Html.PartialAsync("Provider", Model.Provider))</div>
        <div class="col-sm-4"> @(await Html.PartialAsync("ReportDetails", Model.ReportDetails))</div>
    </div>
    @if (!string.IsNullOrWhiteSpace(Model.PatientReportBlockConsentNote))
    {
    <div class="alert alert-danger text-center" role="alert">
        <p class="red">@Html.Raw(Model.PatientReportBlockConsentNote)</p>
    </div>
    }
    <div class="comment-section">
        <div id="<EMAIL>" class="row panel-collapse collapse">
            <div class="col-md-12">
                @*width: 100%; display: block; clear: both; background: #4cff00*@
            <div style="float: left; border-top: 1px solid #c5c1c1; width: 100%" class="olis-heading">Comments</div>
            @*<hr />*@
            <div class="dont-break-out fixedWidthfont" style="float: left;">
                @foreach (var c in Model.ReportNotes)
                {
                    string comment = c;
                    <div>@Html.Raw(comment)</div><br />
                }
            </div>
        </div>
    </div>


    @if (Model.Patient != null && (!string.IsNullOrWhiteSpace(Model.Patient.OrderNotes)))
    {
        <div id="<EMAIL>" class="row panel-collapse collapse">
            <div class="col-md-12">
                <h4>Comments (orderNote)</h4>
                <div class="dont-break-out fixedWidthfont">@Html.Raw(Model.Patient.OrderNotes.FormatHL7Report())</div>
                <hr />
            </div>
        </div>
    }
</div>
</div>


<div class="requested-tests" style="width: 100%; margin-bottom: 10px">
    @(await Html.PartialAsync("TestCategories", Model.TestCategories))
</div>

<div class="row">
    <div class="col-md-8">@(await Html.PartialAsync("OLISCCList", Model.CCList))</div>    
</div>
<div class="row">
    <div class="col-md-8">@(await Html.PartialAsync("OLISOrderingFacility", Model.OrderingFacility))</div>   
</div>
<div class="row">
    <div class="col-md-8">
        @if (Model.AdmittingProvider != null)
        {
            <div>
                <strong> Admitting Provider:</strong>@(await Html.PartialAsync("PractitionerProviderCompact", Model.AdmittingProvider))
            </div>
        }
        @if (Model.AttendingProvider != null)
        {
            <div>
                <strong>Attending Provider :</strong>
                @(await Html.PartialAsync("PractitionerProviderCompact", Model.AttendingProvider))
            </div>
        }
    </div>
</div>