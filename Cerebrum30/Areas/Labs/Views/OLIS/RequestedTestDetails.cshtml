﻿@model IEnumerable<Cerebrum.ViewModels.OLIS.VMReportDetails>

@using Cerebrum30.Utility;

@{
    var reportId = Model.FirstOrDefault().ReportId;
}
<table id="requested-tests-details_@reportId" class="table table-striped">
    <thead>
        <tr>
            <th>Last Updated in OLIS</th>
            <th class="notForPrint">Order Note</th>
            <th>Test Group</th>
            <th>Test Request Name</th>
            <th>Diagnosis</th>
            <th>Test Request Notes</th>
            <th>Status</th>
            <th>Specimen Type</th>
            <th>Collection DateTime</th>
            <th>Collector Comment</th>
            <th>Test Result Name</th>
            <th>Status</th>
            <th>Result Value</th>
            <th>Flag</th>
            <th>Reference Range</th>
            <th>Units</th>
            <th>Result Notes</th>
            <th>Attachment</th>
            <th>Full Report</th>
            <th>Ordering Provider</th>
        </tr>
    </thead>
    <tbody>
@foreach (var rm in Model)
{
    bool antibioticFlag = false;
    var allTests = rm.RequestedTests;
    var subresult = allTests.SelectMany(s => s.Results).Where(w => w.SubResult != null).Select(sm => sm.SubResult).ToList();
    if (subresult != null && subresult.Any())
    {
        allTests.AddRange(subresult);
        antibioticFlag = true;
    }
    //allTests = allTests.OrderBy(o => o.testRequestSortKey).ToList();
    string lastTest = string.Empty;


    foreach (var item in allTests)
    {
        var lisrofresults = new List<Cerebrum.ViewModels.OLIS.VMOLISResult>();


        if (item.AncillaryResults.Any())
        {
            lisrofresults.AddRange(item.AncillaryResults.ToList());
        }
        lisrofresults.AddRange(item.Results.ToList());
        var results = lisrofresults;// item.Results.ToList();
        if (results.Any())
        {
            foreach (var r in results)//.OrderBy(t => t.testRequestSortKeyZBR11)
            {
                string cls = r.ResultStatus == AwareMD.Cerebrum.Shared.Enums.OLISResultStatus.W ? "strikeText" : "";
                string colorClass = "NormalResult";
                string resultFlagClass = "NormalResultFlag";
                if (r.ResultStatus != AwareMD.Cerebrum.Shared.Enums.OLISResultStatus.F)
                {
                    colorClass = "AbnormalResult";
                }
                else
                {
                    colorClass = "NormalResult";
                }

                if ((string.IsNullOrWhiteSpace(r.Flag)) || r.Flag.Equals("N"))
                {
                    resultFlagClass = "NormalResultFlag";
                }
                else
                {
                    resultFlagClass = "AbnormalResultFlag";
                }
                    <tr>
                        <td style="font-size:small">@Html.DisplayFor(modelItem => item.LastUpdatedInOLISString)</td>
                        <td>
                            @if (!string.IsNullOrWhiteSpace(rm.OrderNote))
                            {
                                <a class="popoverButton"
                                   data-toggle="popover"
                                   data-html="true"
                                   title="Notes"
                                   data-trigger="focus"
                                   tabindex="0"
                                   data-content="@Html.DisplayFor(modelItem => rm.OrderNote)"><span class="glyphicon glyphicon-file"></span></a>
                            }
                        </td>

                        <td>
                            @Html.DisplayFor(modelItem => item.TestCategory)

                        </td>

                        <td>
                            @if (string.IsNullOrWhiteSpace(lastTest) || ((!string.IsNullOrWhiteSpace(lastTest)) && (!lastTest.Trim().ToLower().Equals(item.TestGroupOBR41.Trim().ToLower()))))
                            {
                                @Html.DisplayFor(modelItem => item.TestGroupOBR41)

                            }
                            @if (item.BlockConsent)
                            {
                                <span class='red'>(Do not disclose without express patient consent)</span>
                            }
                        </td>
                        <td>@Html.Raw(string.Join("<br/>", item.Diagnosis))</td>
                        <td>
                            @if (!string.IsNullOrWhiteSpace(item.SpecimenComment))
                            {
                                <a class="popoverButton"
                                   data-toggle="popover"
                                   data-html="true"
                                   title="Notes"
                                   data-trigger="focus"
                                   tabindex="0"
                                   data-content="@Html.DisplayFor(modelItem => item.SpecimenComment)"><span class="glyphicon glyphicon-file"></span></a>
                            }
                        </td>
                        <td>@Html.Raw(item.testRequestStatusDescription)</td>
                        <td>@Html.DisplayFor(modelItem => item.SpecimenTypeOBR15)</td>
                        <td>@Html.DisplayFor(modelItem => item.CollectionDateTime)</td>
                        <td>
                            @if (!string.IsNullOrWhiteSpace(item.CollectorComment))
                            {
                            <a class="popoverButton"
                               data-toggle="popover"
                               data-html="true"
                               title="Notes"
                               data-trigger="focus"
                               tabindex="0"
                               data-content="@Html.DisplayFor(modelItem => item.CollectorComment)"><span class="glyphicon glyphicon-file"></span></a>
                            }
                    </td>

                    <td class="@cls">@Html.DisplayFor(modelItem => r.TestName)
                        @if (r.NatureOfAbnormal != null)
                        {
                            @Html.Raw("(" + r.NatureOfAbnormalDescription + ")")
                        }
                        </td>
                    <td class="@colorClass @resultFlagClass">@Html.Raw(r.ResultStatusOBX11)</td>
                    <td class="fixedWidthfont fixedWidthChr">
                        @if (r.ValueType.Equals("FT") || r.ValueType.Equals("TX") || r.ValueType.Equals("ST"))
                        {
                            @Html.Raw(r.Result.FormatHL7Report())
                        }
                        else
                        {
                            @Html.Raw(r.Result)
                        }

                        @if (antibioticFlag && (item.ResultAbbrivationInfo.Trim() != ""))
                        {
                            <a class="popoverButton" data-toggle="popover" title="Notes" data-trigger="focus" tabindex="0"
                               data-content="@Html.DisplayFor(modelItem => item.ResultAbbrivationInfo)"><span class="glyphicon glyphicon-info-sign"></span></a>
                        }

                    </td>
                    <td class="@colorClass @resultFlagClass @cls">@Html.Raw(r.Flag)</td>
                    <td class="@resultFlagClass @cls">@Html.Raw(r.ReferenceRange)</td>
                    <td class="@resultFlagClass @cls">@Html.Raw(r.Units)</td>
                    <td>
                        @if (!string.IsNullOrWhiteSpace(r.SpecimenResultComment))
                        {
                        <a class="popoverButton" data-toggle="popover" title="Notes" data-trigger="focus" tabindex="0"
                           data-content="@Html.DisplayFor(modelItem => r.SpecimenResultComment)"><span class="glyphicon glyphicon-file"></span></a>
                        }
                </td>
                <td>
                    @if (r.ValueType.Equals("ED"))
                    {
                        <a href="@r.AttachedReport.URL">
                            <span class="glyphicon glyphicon-file"></span>
                        </a>
                    }
                </td>
                <td><a href="@rm.FullReportURL" target="_blank"><span class="glyphicon glyphicon-file"></span></a></td>
                <td>@Html.DisplayFor(modelItem => item.Provider.OrderedBy.LicenceNo)
                        @Html.DisplayFor(modelItem => item.Provider.OrderedBy.FullName)</td>
            </tr>
                    lastTest = item.TestGroupOBR41;
                }
                lastTest = string.Empty;
            }
            else
            {
                <tr>
                    <td style="font-size:small">
                        @Html.DisplayFor(modelItem => item.LastUpdatedInOLISString)
                    </td>
                    <td>
                        @if (!string.IsNullOrWhiteSpace(rm.OrderNote))
                        {
                            <a class="popoverButton"
                               data-toggle="popover"
                               data-html="true"
                               title="Notes"
                               data-trigger="focus"
                               tabindex="0"
                               data-content="@Html.DisplayFor(modelItem => rm.OrderNote)"><span class="glyphicon glyphicon-file"></span></a>
                        }
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.TestCategory)
                    </td>

                    <td>
                        @if (string.IsNullOrWhiteSpace(lastTest) || ((!string.IsNullOrWhiteSpace(lastTest)) && (!lastTest.Trim().ToLower().Equals(item.TestGroupOBR41.Trim().ToLower()))))
                        {
                            @Html.DisplayFor(modelItem => item.TestGroupOBR41)

                        }
                    </td>
                    <td>@Html.Raw(string.Join("<br/>", item.Diagnosis))</td>
                    <td>
                        @if (!string.IsNullOrWhiteSpace(item.SpecimenComment))
                        {
                            <a class="popoverButton"
                               data-toggle="popover"
                               data-html="true"
                               title="Notes"
                               data-trigger="focus"
                               tabindex="0"
                               data-content="@Html.DisplayFor(modelItem => item.SpecimenComment)"><span class="glyphicon glyphicon-file"></span></a>
                        }
                    </td>
                    <td>
                        @Html.Raw(item.testRequestStatusDescription)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.SpecimenTypeOBR15)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.CollectionDateTime)
                    </td>
                    <td>
                        @if (!string.IsNullOrWhiteSpace(item.CollectorComment))
                        {
                            <a class="popoverButton" data-toggle="popover" title="Notes" data-trigger="focus" tabindex="0"
                               data-content="@Html.Raw(item.CollectorComment)"><span class="glyphicon glyphicon-file"></span></a>
                        }
                    </td>

                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Provider.OrderedBy.LicenceNo)
                    @Html.DisplayFor(modelItem => item.Provider.OrderedBy.FullName)
                </td>
                </tr>
                lastTest = item.TestGroupOBR41;
            }


        }
            }
    </tbody>
</table>





