﻿@model IEnumerable<Cerebrum.ViewModels.Medications.VMPatientMedication>
@using System.Text;
@using Cerebrum.ViewModels.Medications;

@functions{
    public string getMedicationBrand(VMPatientMedication item)
    {
        StringBuilder sb = new StringBuilder();
        sb.Append(item.MedicationName?.Trim());
        sb.Append(" ");

        sb.Append(item.Strength?.Trim());
        if (!string.IsNullOrEmpty(item.Dose))
        {
            sb.Append(", Dose:");
            sb.Append(item.Dose);
        }

        if (!string.IsNullOrEmpty(item.SIG))
        {
            sb.Append(", SIG:" + item.SIG);
        }

        if (!string.IsNullOrEmpty(item.MitteStr))
        {
            sb.Append(", Mitte:");
            sb.Append(item.Mitte);
        }
        if (!string.IsNullOrEmpty(Convert.ToString(item.Repeats)))
        {
            sb.Append(", Repeats:");
            sb.Append(item.Repeats);
        }

        sb.Append(" ");

        return sb.ToString();
    }

}
<input type="hidden" id="hdEmrResultReturned" value="@Model.Count()" />
@if (Model.Count() > 0)
{
    <table class="table-medication-comparative-view" id="emr-medication-comparative-view">
        <thead>
            <tr>
                <th class="text-center text-nowrap">
                    <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="DateWritten">
                        Created Date
                    </a><span class="span-sort-order" id="span-DateWritten-up">&#9650;</span><span class="span-sort-order" id="span-DateWritten-down">&#9660;</span>
                </th>
                <th class="text-center text-nowrap">
                    <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="StartDate">
                        Start Date
                    </a><span class="span-sort-order" id="span-StartDate-up">&#9650;</span><span class="span-sort-order" id="span-StartDate-down">&#9660;</span>
                </th>
                <th class="text-center text-nowrap">
                    <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="Medication">
                        Medication
                    </a><span class="span-sort-order" id="span-Medication-up">&#9650;</span><span class="span-sort-order" id="span-Medication-down">&#9660;</span>
                </th>
                <th class="text-center text-nowrap">
                    <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="Prescriber">
                        Prescriber
                    </a><span class="span-sort-order" id="span-Prescriber-up">&#9650;</span><span class="span-sort-order" id="span-Prescriber-down">&#9660;</span>
                </th>
                <th class="text-center text-nowrap">
                    <a href="javascript:void(null)" title="Click to sort" class="btn-dhdr-sort-column" data-sort-column="DIN">
                        DIN
                    </a><span class="span-sort-order" id="span-DIN-up">&#9650;</span><span class="span-sort-order" id="span-DIN-down">&#9660;</span>
                </th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                string prescriber = item.LastPrescribedName; 
            <tr>
                <td class="text-nowrap bold">@item.DateCreated.ToString("yyyy/MM/dd")</td>
                <td class="text-nowrap bold">@item.DateStarted.ToString("yyyy/MM/dd")</td>
                <td>@getMedicationBrand(item)</td>
                <td>@prescriber</td>
                <td>@item.DIN</td>
            </tr>
            }
        </tbody>
    </table>
}

