﻿@model Cerebrum.ViewModels.Bill.RemittanceDetailResponse
@using Newtonsoft.Json;
@using Cerebrum.ViewModels.Bill;

@{
    ViewBag.Title = "Report";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";
}

@section customcss {
    <link rel="stylesheet" href="Areas/Schedule/Content/shared-styles.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/schedule.css" />
    <link rel="stylesheet" href="Areas/Schedule/Content/appointments-modal.css" />
}

<script type="text/javascript" src="~/Areas/Bill/Scripts/RemittanceDetail.js"></script>
<style>
    .ui-dialog-titlebar {
        min-height: 56px !important;
        border-bottom: 1px solid #ebebeb !important;
        color: rgb(90, 89, 89);
        background: rgb(247, 247, 247);

    }
    .ui-dialog-title {
        font-size: 18px;
        font-weight: normal !important;
        padding-top:8px;
    
    }
    #buttonPrintRemittanceDetail {
        font-family: '<PERSON>', sans-serif !important;
    }
    .ui-icon-closethick {
        display: none;
    }
    .ui-dialog-titlebar-close {
        background-size: 44% !important;
    }
</style>
<div class="col-sm-12" style="margin-top: 16px;" id="remittanceDetailDiv" name="remittanceDetailDiv">
    @if (string.IsNullOrEmpty(Model.errorMessage))
    {
        <div class="row">
            <div class="col-sm-8" style="font-size: 18px; font-weight: bold;">
                Remittance Advice File: @Model.fileName
            </div>
            <div class="col-sm-4 text-right vertical-center hidden-print">
                <input type="button" id="buttonPrintRemittanceDetail" name="buttonPrintRemittanceDetail" value="Print" class="btn btn-default btn-sm btn-primary" />
            </div>
        </div>
        int i = 0;
        string tableData = string.Empty;
        do
        {
            var detailData = Model.detailDatas[i];
            switch (detailData.dataType)
            {
                case "HR1":
                    var hr1 = JsonConvert.DeserializeObject<RemittanceDetailHR1>(detailData.dada);
                    <div class="row">
                        <div class="col-sm-12 66456456">
                            Payee Name: @hr1.payeeName
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Total Amount Payable: @hr1.totalAmountPayable
                        </div>
                    </div>
                        break;
                    case "HR2":
                        var hr2 = JsonConvert.DeserializeObject<RemittanceDetailHR2>(detailData.dada);
                        string address = hr2.address;
                        if (i < Model.detailDatas.Count - 1 && Model.detailDatas[i + 1].dataType == "HR3")
                        {
                            var hr3 = JsonConvert.DeserializeObject<RemittanceDetailHR3>(Model.detailDatas[i + 1].dada);
                            address += "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" + hr3.address;
                            i += 1;
                        }
                        <div class="row">
                            <div class="col-sm-12">Address: @Html.Raw(address)
                            </div>
                        </div>
                    break;
                case "HR4":
                    var hr4 = JsonConvert.DeserializeObject<RemittanceDetailHR4>(detailData.dada);
                   <div class="col-sm-12" style="font-weight: bold; margin-top: 8px;">
                        Doctor: @hr4.doctorName
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        Accounting #: @Html.Raw(string.Format("<a href='' onclick='showClaim(\"{0}\", \"{1}\"); return false;'><span style='color: Maroon;'>{2}</span></a>", hr4.appointmentId, hr4.admissionActionId, hr4.appointmentId + hr4.admissionActionId))
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        Health Registration: @hr4.healthRegistration
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        Patient: 
                        @if (hr4.patientRecordId <= 0)
                        {
                            @(hr4.patientName)
                        }
                        else
                        {
                            <div class="btn-popover-container">
                                <span class="popover-btn popover-pointer cb-text16 text-primary">@(hr4.patientName)</span>
                                <div class="btn-popover-title">
                                    <span class="default-text-color">e-Chart</span>
                                </div>
                                <div class="btn-popover-content">
                                    @*@{Html.RenderAction("GetPatientMenu", "Patients", new { area = "", Id = hr4.patientRecordId }); }*@
                                    @{
                                        var patientMenu = new Cerebrum.ViewModels.Patient.VMPatientMenu();
                                        patientMenu.PatientId = hr4.patientRecordId;
                                        //patientMenu.AppointmentId = Model.AppointmentId;
                                        //patientMenu.Practiceid = Model.PracticeId;
                                    }
                                    @{await Html.RenderPartialAsync("_PatientMenu", patientMenu); }
                                </div>
                            </div>
                        } 
                    </div>
                    break;
                case "HR5":
                    var hr5 = JsonConvert.DeserializeObject<RemittanceDetailHR5>(detailData.dada);
                    tableData = "<tr><td>" + hr5.claimNumber + "</td><td>" + hr5.transactionType + "</td><td>" + hr5.serviceDate + "</td>";
                    tableData += "<td>" + hr5.numberofServices + "</td><td>" + hr5.serviceCode + "</td><td>" + hr5.submitted + "</td>";
                    tableData += "<td>" + hr5.paid + "</td><td>" + hr5.explanatoryCode + "</td></tr>";
                    break;
                case "HR6":
                    var hr6 = JsonConvert.DeserializeObject<RemittanceDetailHR6>(detailData.dada);
                    tableData = "<tr><td colspan='8' style='text-align: left;'>Amount Brought Forward – Claims Adjustment: " + hr6.claimsAdjustment;
                    tableData += "<br />Amount Brought Forward – Advances: " + hr6.advances;
                    tableData += "<br />Amount Brought Forward – Reductions: " + hr6.reductions + "</td></tr>";
                    break;
                case "HR7":
                    var hr7 = JsonConvert.DeserializeObject<RemittanceDetailHR7>(detailData.dada);
                    tableData = "<tr><td colspan='8' style='text-align: left;'>Transaction Code: " + hr7.transactionCode;
                    tableData += "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Transaction Date: " + hr7.transactionDate;
                    tableData += "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Amount: " + hr7.amount;
                    tableData += "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Message: " + hr7.message + "</td></tr>";
                    break;
                case "HR8":
                    var hr8 = JsonConvert.DeserializeObject<RemittanceDetailHR8>(detailData.dada);
                    tableData = "<tr><td colspan='8' style='text-align: left;'>" + hr8.message + "&nbsp;</td></tr>";
                    break;
            }

            if (detailData.dataType == "HR5" || detailData.dataType == "HR6" || detailData.dataType == "HR7" || detailData.dataType == "HR8")
            {
                if (!(i > 0 && (Model.detailDatas[i - 1].dataType == "HR5" || Model.detailDatas[i - 1].dataType == "HR6" || Model.detailDatas[i - 1].dataType == "HR7" || Model.detailDatas[i - 1].dataType == "HR8")))
                {
                    tableData = @"<div class='col-sm-12'>
                                        <table class='table table-striped' style='width: 98%; margin-left: 32px;'>
                                            <thead>
                                                <tr>
                                                    <td>Claim #</td>
                                                    <td>Transaction Type</td>
                                                    <td>Service Date</td>
                                                    <td>Number of Services</td>
                                                    <td>Service Code</td>
                                                    <td>Submitted</td>
                                                    <td>Paid</td>
                                                    <td>Explanatory Code</td>
                                                </tr>
                                            </thead>
                                            <tbody>" + tableData;
                }
                if (i == Model.detailDatas.Count - 1 || !(i < Model.detailDatas.Count - 1 && (Model.detailDatas[i + 1].dataType == "HR5" || Model.detailDatas[i + 1].dataType == "HR6" || Model.detailDatas[i + 1].dataType == "HR7" || Model.detailDatas[i + 1].dataType == "HR8")))
                {
                    tableData += @"</tbody>
                                    </table>
                                </div>";
                }
                @Html.Raw(tableData)
            }
            i += 1;
        }
        while (i < Model.detailDatas.Count);
        if (Model.doctorRADatas.Count > 0)
        {
            <div class="col-sm-12" style="margin-top: 24px; margin-bottom: 48px;">
                <table class='table table-striped' style="width: 98%; margin-left: 32px;">
                    <tr>
                        <td>Doctor</td>
                        <td>Billing Number</td>
                        <td>Submitted (Total)</td>
                        <td>Submitted (Professional)</td>
                        <td>Submitted (Technical)</td>
                        <td>Paid (Total)</td>
                        <td>Paid (Professional)</td>
                        <td>Paid (Technical)</td>
                    </tr>
                    @foreach (var doctorData in Model.doctorRADatas)
                    {
                        <tr>
                            <td>@doctorData.doctorName</td>
                            <td>@doctorData.billingNumber</td>
                            <td>@string.Format("{0:C}", ((float)doctorData.submittedTotal) / 100)</td>
                            <td>@string.Format("{0:C}", ((float)doctorData.submittedProfessional) / 100)</td>
                            <td>@string.Format("{0:C}", ((float)doctorData.submittedTechnical) / 100)</td>
                            <td>@string.Format("{0:C}", ((float)doctorData.paidTotal) / 100)</td>
                            <td>@string.Format("{0:C}", ((float)doctorData.paidProfessional) / 100)</td>
                            <td>@string.Format("{0:C}", ((float)doctorData.paidTechnical) / 100)</td>
                        </tr>
                    }
                </table>
            </div>
        }
        <br /><br /><br />
    }
    else
    {
        <div class="col-sm-12 text-center text-danger">
            <br /><br /><br /><br /><br /><br /><br /><br />
            <h1>@Model.errorMessage</h1>
            <br /><br /><br /><br /><br /><br /><br /><br />
        </div>
    }
</div>
<div id="claimDialog" name="claimDialog" class="col-sm-12" style="display:none;"></div>