﻿@model  Cerebrum.ViewModels.VP.VMCPPAlertList

<script>
    $(document).ready(function () {
        initPopover();
    });

    function editAlert(element) {
        var _patientid = $(element).attr("patientid");
        var _rowid = $(element).attr("rowid");

        var data = { entryID: _rowid, patientID: _patientid};
        url = 'VP/VP/Edit_Alert_CPP';

        $("#" + modalTemplateContentId).width("800px");
        //$("#" + modalTemplateBodyId).height(height);
        $("#" + modalTemplateTitleId).text("Edit");

        ajaxCall(url, data, false, function (data) {
            $("#" + modalTemplateBodyId).html(data);
            $("#" + modalTemplateId).on("shown.bs.modal", function () { CPPEditModalMode = true; });
            $("#" + modalTemplateId).on("hidden.bs.modal", function () { CPPEditModalMode = false; });
            $("#" + modalTemplateId).modal({ keyboard: false, backdrop: "static" }, "show");
        });

        return false;
    }
</script>

<div class="" id="divGrd" style="padding-top:30px;">
    <div class="row">
        <div class="col-lg-12">
            <div class="pre-scrollable">
                <table class="spanCell table table table-striped table-bordered table-condensed">
                    <tr>
                        <th>
                            Show
                        </th>
                        <th>
                            Description
                        </th>
                        <th>
                            Notes
                        </th>
                        <th>
                            Date Active
                        </th>
                        <th>
                            End Date
                        </th>
                        <th>
                            Submit Date
                        </th>
                        <th>
                        </th>
                    </tr>
                    @for (int i = 0; i < @Model.Alerts.Count; i++)
                    {
                        string dateActive = ((Model.Alerts[i].DateActive_Month == 0 ? string.Empty : Model.Alerts[i].DateActive_Month.ToString()) + (Model.Alerts[i].DateActive_Day == 0 ? string.Empty : "/" + Model.Alerts[i].DateActive_Day.ToString()) + (Model.Alerts[i].DateActive_Year == 0 ? string.Empty : "/" + Model.Alerts[i].DateActive_Year.ToString())).Trim('/');
                        string endDate = ((Model.Alerts[i].EndDate_Month == 0 ? string.Empty : Model.Alerts[i].EndDate_Month.ToString()) + (Model.Alerts[i].EndDate_Day == 0 ? string.Empty : "/" + Model.Alerts[i].EndDate_Day.ToString()) + (Model.Alerts[i].EndDate_Year == 0 ? string.Empty : "/" + Model.Alerts[i].EndDate_Year.ToString())).Trim('/');
                        <tr>
                            <th>
                                @Html.CheckBoxFor(model => model.Alerts[i].Visible, new { @disabled = "disabled" })
                            </th>
                            <th>
                                @Html.TextAreaFor(model => model.Alerts[i].Description, new { @cols = 25, @rows = 5 })
                            </th>
                            <th>
                                @Html.TextAreaFor(model => model.Alerts[i].Notes, new { @cols = 25, @rows = 5 })
                            </th>
                            <th>
                                @dateActive
                            </th>
                            <th>
                                @endDate
                            </th>
                            <th>
                                @Html.DisplayFor(model => model.Alerts[i].eSubmitDate)
                            </th>
                            <th>
                                @Html.ActionLink("Edit", "Edit_Alert_CPP", new { entryID = Model.Alerts[i].Id, patientID = Model.PatientID },
                               new
                               {
                                   @patientid = @Model.PatientID,
                                   @rowid = @Model.Alerts[i].Id,
                                   onclick = "return editAlert(this);"
                               })
                                <br /><br />
                                <a href="#" id="<EMAIL>[i].Id" data-name="<EMAIL>[i].Id"  onclick="return cppButtonDeleteClicked(this);" data-patientid="@Model.PatientID" data-cpptype="6" data-rowid="@Model.Alerts[i].Id">Delete</a>
                                <br /><br />
                                <div class="btn-popover-container">
                                    <button type="button" class="btn btn-default btn-xs popover-btn" data-original-title="" title="" data-toggle="popover">
                                        <span class="glyphicon glyphicon-th-list" style="color: #337ab7;"></span>
                                    </button>
                                    <div class="btn-popover-title">Residual Information</div>
                                    <div class="btn-popover-content">@Html.Raw(Model.Alerts[i].ResidualInformation.Replace("\n", "<br />"))</div>
                                </div>
                            </th>

                        </tr>
                    }

                </table>
            </div>
        </div>
    </div>
</div>







