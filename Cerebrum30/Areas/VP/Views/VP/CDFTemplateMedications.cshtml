﻿@model Cerebrum.ViewModels.VP.VMCDFPatientMedication

<div style="text-align: left;width:200px;height:100px;overflow-y:scroll;overflow-x:hidden"  >
    @{ 
        var medlist = Model.Medications.Where(m => m.DateStarted <= @Model.DateStarted).ToList();
    }
    @foreach (var med in medlist)
    {
        if (Model.CDFTemplateMedications.Any(a => a.CDFTemplate == "DM" && med.TherapeuticClasses.Any(ma=>ma.ToLower().Trim().Contains(a.TherapeuticClass.ToLower()))))
        {
            <span class="diabetic-cdf-med-template-highlight" style="background-color:coral">
                @($"{med.MedicationName} {med.Dose} {med.SIG}".Trim())
            </span>
            <br />
        }
        else if (Model.CDFTemplateMedications.Any(a => a.CDFTemplate == "HF" && med.TherapeuticClasses.Any(ma => ma.<PERSON>().Trim().Contains(a.TherapeuticClass.ToLower()))))
        {
            <span class="heart-cdf-med-template-highlight" style="background-color:palegreen">
                @($"{med.MedicationName} {med.Dose} {med.SIG}".Trim())
            </span>
            <br />
        }
        else
        {
            @($"{med.MedicationName} {med.Dose} {med.SIG}".Trim())
            <br />
        }

    }
   
</div>
