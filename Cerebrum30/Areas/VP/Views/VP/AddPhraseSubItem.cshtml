﻿@model Cerebrum30.Areas.VP.Models.ViewModels.AddPhraseSubItem_VM
@{
    ViewBag.ModuleName = "Add Report Phrase Item";
    Layout = "~/Views/Shared/_LayoutFluidPopup.cshtml";
}
@using (Html.BeginForm("AddPhraseSubItem", "VP", FormMethod.Post, new { @id = "frm-add-rp", model = @Model }))
{
    <div class="container">
        <form class="form-horizontal">

            @Html.HiddenFor(x => x.RootPhraseID)
            @Html.HiddenFor(x => x.ParentID)
            @Html.HiddenFor(x => x.DocID)
            <br />
            <br />
            <br />
            <div class="form-group">
                <label class="col-sm-4 control-label text-right" for="name">Create New</label>
                <div class="col-sm-8">
                    @foreach (var value in Enum.GetValues(Model.ItemType.GetType()))
                    {
                    <div class="col-sm-2 text-left">
                        @Html.RadioButtonFor(x => x.ItemType, value)
                        @Html.Label(value.ToString())
                    </div>
                    }
                </div>
            </div>
            <div>&nbsp;</div>
            <div>&nbsp;</div>
            <div class="form-group">
                <label class="col-sm-4 control-label text-right" for="name">Item Name</label>
                <div class="col-sm-8">
                    @Html.TextBoxFor(x => x.Name, new { @class = "form-control input-sm" })
                </div>
            </div>
            <div>&nbsp;</div>
            <div class="form-group">
                <label class="col-sm-4 control-label text-right" for="val">Item Value</label>
                <div class="col-sm-8">
                    @Html.TextAreaFor(x => x.Value, new { @class = "form-control input-sm", @rows = "5", @cols = "50" })
                </div>
            </div>
            <div class="row">&nbsp;</div>
        </form>
        <br />

        <div class="row">
            <div class="col-md-12 text-center">
                <span id="span-error-mssg-cus" style="color:red;">
                </span>
            </div>
        </div>

        <br />

        <div class="row">
            <div class="col-sm-12">
                <table cellpadding="3" class="table text-center">
                    <tr>
                        <td>
                            <a data-url='@Url.Action("AddPhraseSubItem")'
                               id="btn-create-subitem-rp"
                               class="btn btn-primary btn-sm" href="#">Save</a>
                            <a href="#" class="btn btn-default btn-sm btn-cancel-model">Cancel</a>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <br />
    <br />
}


