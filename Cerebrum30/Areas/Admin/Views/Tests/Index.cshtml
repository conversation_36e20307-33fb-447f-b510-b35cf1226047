﻿@model IEnumerable<Cerebrum.ViewModels.Test.VMTest>

@{

    ViewBag.Title = "Tests/Master (Admin)";
    ViewBag.ModuleName = "Tests/Master (Admin)";
    Layout = "~/Views/Shared/_LayoutFluid.cshtml";


}
<script src="~/Scripts/jscolor.js"></script>
<script type="text/javascript">
    function updateColor(id,jscolor) {
        console.log(jscolor);
        $.post("/admin/tests/updatecolor/", { id: id, color: jscolor }, function (msg) {
            toastr.info(msg);
        });
    }
    function LoadAppointmentsByTest(testid) {
        var modalId = '#appointmentsTestModal';
        var url = "/schedule/appointments/ByRequestedTest/";

        if ($(modalId).length <= 0) {
            $('#ajax-loader').show();
            $.ajax({
                url: url,
                data: { testid: testid },
                type: 'GET',
                complete: function () {
                    $('#ajax-loader').hide();
                },
                success: function (data) {
                    loadCBModal(modalId, data);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    checkAjaxError(jqXHR);
                }
            });
        }
    }
    $(document).ready(function () {
        $(".addToPractice").on("click", function (event) {
            event.preventDefault();
            var url = $(this).attr('href');
            var id = $(this).attr('data-value');
            var title = $(this).attr('data-title');
            $.get(url,{testId:id}, function (data) {
                $("#dialog-confirm").html(data);
                $("#dialog-confirm").dialog("open");
                $("#dialog-confirm").dialog({title:title});

            });

        });
        $(".test-active").change(function () {
            var testId = $(this).attr("data-value");
            $.post('/admin/tests/activedeactive/', { id: testId }, function (msg) {
                if (msg == '0') {
                    LoadAppointmentsByTest(testId);
                }
                else {
                    showNotificationMessage("success", msg);
                }

            });

        });
        $("#dialog-confirm").dialog({
            autoOpen: false,
            resizable: false,
            dialogClass: 'info',
            height: 'auto',
            width: 'auto',
            draggable: true,

            buttons: {
                Cancel: function () {
                    $(this).dialog("close");
                },
                "Save": function () {
                    var selectedVal = [];
                    var selected = $(this).find("[id*=practiceTests] option:selected");

                    selected.each(function (i, selected) {
                        selectedVal[i] = $(selected).val();
                        console.log(selectedVal[i]);
                    });
                    var testid=$(this).find("#testId").val();
                    var action = $(this).find('form').attr('action');
                    $.post(action, { testId: testid ,practices:selectedVal}, function (data) {
                        if(data=='1')
                            toastr.info('Updated Successfully');

                    });
                    $(this).dialog("close");
                }
            }
        });
    });

</script>
@*<h2>Tests</h2>
<h4>Master</h4>*@
<div style="margin-top:15px"></div>
<div id="dialog-confirm" title="Select Practice">

</div>
@if (CerebrumUser.HasRole($"Super Admin"))
{
<p>
    @Html.ActionLink("Create New", "Create")
</p>
<p>
    @Html.ValidationSummary(false, "", new { @class = "text-danger" })
</p>
<table class="table _435643647" id="table-TestMaster">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.color)
            </th>
            <th>
                @Html.ActionLink("Full Name", "Index", new { sortBy = "Full_Name", sortOrder = ViewBag.SortParm.ToString() == "Full_Name" ? ViewBag.SortOrderParm : false })
            </th>
            <th>
                @Html.ActionLink("Short Name", "Index", new { sortBy = "Short_Name", sortOrder = ViewBag.SortParm.ToString() == "Short_Name" ? ViewBag.SortOrderParm : false })
            </th>
            <th>
                @Html.ActionLink("order", "Index", new { sortBy = "order", sortOrder = ViewBag.SortParm.ToString() == "order" ? ViewBag.SortOrderParm : false })
            </th>
            <th>
                @Html.ActionLink("duration", "Index", new { sortBy = "duration", sortOrder = ViewBag.SortParm.ToString() == "duration" ? ViewBag.SortOrderParm : false })
            </th>
            <th>
                @Html.DisplayNameFor(model => model.IsRadiology)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.modality)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.HrmTypeShort)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.HrmTypeLong)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.HrmModality)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.LoincCode)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.isActive)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.updatedDateTime)
            </th>
            <th></th>
        </tr>
    </thead>
    @foreach (var item in Model)
    {
<tr>
    <td>
        <input class="jscolor" onchange="updateColor('@item.Id',''+this.jscolor)" value="@item.color">
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.testFullName)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.testShortName)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.order)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.duration)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.IsRadiology)
    </td>
    <td>
        @if (item.Modalities != null && item.Modalities.Count() > 0)
        {
            @Html.Encode(string.Join(",", item.Modalities.Select(s => s.modalityName)))

        }

    </td>
    <td>
        @Html.DisplayFor(modelItem => item.HrmTypeShort)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.HrmTypeLong)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.HrmModality)
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.LoincCode)
    </td>
    <td>
        <input type="checkbox" class="test-active" value="@item.isActive" @(item.isActive ? "checked" : "") data-value="@item.Id" />
    </td>
    <td>
        @Html.DisplayFor(modelItem => item.updatedDateTime)
    </td>
    <td>
        @Html.ActionLink("Edit", "Edit", new { id = item.Id }) |
        @Html.ActionLink("Details", "Details", new { id = item.Id }) |
        <a href="/admin/tests/AddUpdatePracticeTests" data-value="@item.Id" data-title="@item.testFullName" class="addToPractice">Add/Update To Practice</a>
    </td>
</tr>
    }

</table>
}
<br />
<br />
