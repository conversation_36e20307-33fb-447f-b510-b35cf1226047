﻿@*@model Cerebrum.ViewModels.Triage.VMWaitListSearch*@


@{
    var totalItems = (int)ViewBag.TotalItems;
    var triageURL = Html.Raw((string)ViewBag.TriageURL);
}

@if (ViewBag.Paging != null && Convert.ToBoolean(ViewBag.Paging))
{
    var totalPages = (int)ViewBag.TotalPages;
    var pageNumber = (int)ViewBag.PageNumber;
    var currentPaging = (string)ViewBag.CurrentPaging;
    var pagingUrls = (List<Cerebrum.ViewModels.Common.VMLookupItem>)ViewBag.PagingUrls;
    <div class="row">
        <div class="col-md-12 text-right">
            <span>@currentPaging</span>
            <select class="drp-triage-paging">
                @foreach (var item in pagingUrls)
                {
                    if (item.Text == pageNumber.ToString())
                    {
                        <option value="@item.Value" selected>Page @item.Text</option>
                    }
                    else
                    {
                        <option value="@item.Value">Page @item.Text</option>
                    }
                }
            </select>
            <span> of </span><span>@totalPages</span>
        </div>
    </div>

}
<script type="text/javascript">
    $(function () {
        $('#triage-list-count').html(@totalItems);
        var triageURL = '@triageURL';
        if (sessionStorage.TriageURL == null) {            
            sessionStorage.TriageURL = triageURL;
            window.history.pushState("string", "Title", triageURL);
        }
        else {
            sessionStorage.removeItem('TriageURL');
        }

    });
</script>


@*@if (ViewBag.Paging != null)
{
    var totalPages = (int)ViewBag.TotalPages;
    var pageNumber = (int)ViewBag.PageNumber;
    var totalItems = (int)ViewBag.TotalItems;
    var disablePrev = pageNumber  <= 0 || totalPages < 2 ? "disabled" : "";
    var disableNext = pageNumber >= totalPages ? "disabled" : "";
    var prevUrl = Url.Action("index", "waitlist", new { area = "triage", Page = pageNumber - 1, StartDate = Model.StartDate, EndDate = Model.EndDate, DoctorId = Model.DoctorId, OfficeId = Model.OfficeId, Patient = Model.Patient, AppointmentTypeItemId = Model.AppointmentTypeItemId, TriageUrgencyId = Model.TriageUrgencyId });
    var nextUrl = Url.Action("index", "waitlist", new { area = "triage", Page = pageNumber + 1, StartDate = Model.StartDate, EndDate = Model.EndDate, DoctorId = Model.DoctorId, OfficeId = Model.OfficeId, Patient = Model.Patient, AppointmentTypeItemId = Model.AppointmentTypeItemId, TriageUrgencyId = Model.TriageUrgencyId });
    <div>
        <span>@pageNumber</span><span> of </span><span>@totalPages</span>
        <a href="@prevUrl" class="btn btn-default btn-sm @disablePrev"><span class="glyphicon glyphicon-chevron-left"></span></a>
        <a href="@nextUrl" class="btn btn-default btn-sm @disableNext"><span class="glyphicon glyphicon-chevron-right"></span></a>
    </div>
    

}*@
