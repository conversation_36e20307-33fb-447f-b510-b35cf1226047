"Code","Message","FilePath","FullLine"
"CS8802","Only one compilation unit can have top-level statements.","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2659,13): error CS8802: Only one compilation unit can have top-level statements. [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0116","A namespace cannot directly contain members such as fields, methods or statements","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2689,87): error CS0116: A namespace cannot directly contain members such as fields, methods or statements [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0116","A namespace cannot directly contain members such as fields, methods or statements","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2693,60): error CS0116: A namespace cannot directly contain members such as fields, methods or statements [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0116","A namespace cannot directly contain members such as fields, methods or statements","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2697,70): error CS0116: A namespace cannot directly contain members such as fields, methods or statements [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0116","A namespace cannot directly contain members such as fields, methods or statements","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2701,71): error CS0116: A namespace cannot directly contain members such as fields, methods or statements [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS1520","Method must have a return type","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2643,13): error CS1520: Method must have a return type [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0501","'<invalid-global-code>.<invalid-global-code>()' must declare a body because it is not marked abstract, extern, or partial","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2643,13): error CS0501: '<invalid-global-code>.<invalid-global-code>()' must declare a body because it is not marked abstract, extern, or partial [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS1520","Method must have a return type","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml(1183,18): error CS1520: Method must have a return type [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0501","'<invalid-global-code>.<invalid-global-code>(Model.errorMessage)' must declare a body because it is not marked abstract, extern, or partial","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml(1183,18): error CS0501: '<invalid-global-code>.<invalid-global-code>(Model.errorMessage)' must declare a body because it is not marked abstract, extern, or partial [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS1520","Method must have a return type","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2651,13): error CS1520: Method must have a return type [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0501","'<invalid-global-code>.<invalid-global-code>()' must declare a body because it is not marked abstract, extern, or partial","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2651,13): error CS0501: '<invalid-global-code>.<invalid-global-code>()' must declare a body because it is not marked abstract, extern, or partial [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0246","The type or namespace name 'Model' could not be found (are you missing a using directive or an assembly reference?)","/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml","    55>/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml(1183,19): error CS0246: The type or namespace name 'Model' could not be found (are you missing a using directive or an assembly reference?) [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS8802","Only one compilation unit can have top-level statements.","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2659,13): error CS8802: Only one compilation unit can have top-level statements. [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0116","A namespace cannot directly contain members such as fields, methods or statements","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2689,87): error CS0116: A namespace cannot directly contain members such as fields, methods or statements [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0116","A namespace cannot directly contain members such as fields, methods or statements","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2693,60): error CS0116: A namespace cannot directly contain members such as fields, methods or statements [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0116","A namespace cannot directly contain members such as fields, methods or statements","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2697,70): error CS0116: A namespace cannot directly contain members such as fields, methods or statements [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0116","A namespace cannot directly contain members such as fields, methods or statements","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2701,71): error CS0116: A namespace cannot directly contain members such as fields, methods or statements [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS1520","Method must have a return type","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2643,13): error CS1520: Method must have a return type [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0501","'<invalid-global-code>.<invalid-global-code>()' must declare a body because it is not marked abstract, extern, or partial","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2643,13): error CS0501: '<invalid-global-code>.<invalid-global-code>()' must declare a body because it is not marked abstract, extern, or partial [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS1520","Method must have a return type","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml(1183,18): error CS1520: Method must have a return type [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0501","'<invalid-global-code>.<invalid-global-code>(Model.errorMessage)' must declare a body because it is not marked abstract, extern, or partial","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml(1183,18): error CS0501: '<invalid-global-code>.<invalid-global-code>(Model.errorMessage)' must declare a body because it is not marked abstract, extern, or partial [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS1520","Method must have a return type","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2651,13): error CS1520: Method must have a return type [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0501","'<invalid-global-code>.<invalid-global-code>()' must declare a body because it is not marked abstract, extern, or partial","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Microsoft.NET.Sdk.Razor.SourceGenerators/Microsoft.NET.Sdk.Razor.SourceGenerators.RazorSourceGenerator/Areas_ExternalDocument_Views_ExternalDocument_Index_cshtml.g.cs(2651,13): error CS0501: '<invalid-global-code>.<invalid-global-code>()' must declare a body because it is not marked abstract, extern, or partial [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
"CS0246","The type or namespace name 'Model' could not be found (are you missing a using directive or an assembly reference?)","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml","         /home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Areas/ExternalDocument/Views/ExternalDocument/Index.cshtml(1183,19): error CS0246: The type or namespace name 'Model' could not be found (are you missing a using directive or an assembly reference?) [/home/<USER>/source/repos/Cerebrum3-upgrade/Cerebrum30/Cerebrum30.csproj]"
